# server
rpc_server_name: user
rpc_port: 11401

# 端口号
http_port: 21401
tcp_port: 31401

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/user
log_json: false
log_kafka_enable: true

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  player:
    addr:  192.168.1.58:6379
    passwd: 8888
  lock:
    addr: 192.168.1.58:6379
    passwd: 8888

mysql_list:
  fancy_player:
    user: root
    addr: 192.168.1.58:3306
    passwd: fancydb2024#
    db: fancy_player


consul_addr: 192.168.1.58:8500

rpc_server_tags: normal

jwt:
  timeout: 876010
  secret: fancygame

kafka-producer:
  brokers: ["192.168.1.58:9092"]
  timeout: 10
