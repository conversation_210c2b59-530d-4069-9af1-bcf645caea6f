package main

import (
	"context"
	"runtime"
	"usersrv/internal/proc"

	"usersrv/internal/server/rpc"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type userService struct {
	Name string
	Ctx  context.Context
}

func (u *userService) Init() error {
	u.Ctx = context.Background()
	u.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infof(u.Name + "服务Init")

	// 初始化RPC Service
	rpc.InitUserRpc()
	proc.InitGmRpc()

	return nil
}

func (u *userService) Start() error {
	return nil
}

func (u *userService) Stop() error {
	return nil
}

func (u *userService) ForceStop() error {
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()
	driver.Run(&userService{})
}
