package test

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func InitSql() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		// "addr":   "localhost:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     dict_mysql.MysqlDBPlayer,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})
}

func InitRedis() {
	viper.SetDefault("redis_list.player.addr", "************:6379")
	viper.SetDefault("redis_list.player.passwd", "8888")
}
