package config

import (
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

const (

	// 查询玩家信息锁
	RDS_GET_PLAYER_SINGLEFLIGHT = "sf:getByUid:%d"
	// 创建玩家锁
	RDS_CREATE_VISITOR_SINGLEFLIGHT = "sf:createVisitor:d:%s_p:%d"
	// 查询玩家id锁
	RDS_GET_PLAYER_ID_BY_SINGLEFLIGHT = "sf:getPlayerIDBy:%s"

	// sfg_PlayerMaster:{ProductId}:{PlayerId}
	SINGLEFLIGHT_MASTER = "sf:p_master:%d:%d"
	// sfg_PlayerInfo:{ProductId}:{PlayerId}
	SINGLEFLIGHT_INFO = "sf:p_info:%d:%d"
	// sfg_PlayerDevice:{ProductId}:{PlayerId}
	SINGLEFLIGHT_DEVICE = "sf:p_device:%d:%d"
	// sfg_PlayerExtend:{ProductId}:{PlayerId}
	SINGLEFLIGHT_EXTEND = "sf:p_extend:%d:%d"
	// sf:getPlayerIDBy:P_D:{productId}:{device}
	SINGLEFLIGHT_MASTER_PRODUCT_DEVICE = "sf:getPlayerIDBy:P_D:%d:%s:%d"
	// sf:getPlayerIDBy:P_A:{productId}:{account}
	SINGLEFLIGHT_MASTER_PRODUCT_ACCOUNT = "sf:getPlayerIDBy:P_A:%d:%s"
	// sf:getPlayerIDBy:P_O:{productId}:{accType}:{openId}
	SINGLEFLIGHT_MASTER_PRODUCT_OPENID = "sf:getPlayerIDBy:P_O:%d:%d:%s"
)

// GetPlayerSingleflight singleflight 查询玩家信息锁
func GetPlayerSingleflight(playerId uint64) string {
	return fmt.Sprintf(RDS_GET_PLAYER_SINGLEFLIGHT, playerId)
}

// CreateVisitorSingleflight singleflight 创建玩家锁
func CreateVisitorSingleflight(productId int32, deviceCode string) string {
	return fmt.Sprintf(RDS_CREATE_VISITOR_SINGLEFLIGHT, deviceCode, productId)
}

// GetPlayerIdBySingleflight singleflight 查询玩家id锁
func GetPlayerIdBySingleflight(key string) string {
	return fmt.Sprintf(RDS_GET_PLAYER_ID_BY_SINGLEFLIGHT, key)
}

// GetSfgMasterKey 单飞PlayerMaster键
func GetSfgMasterKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(SINGLEFLIGHT_MASTER, productId, playerId)
}

// GetSfgInfoKey 单飞PlayerInfo键
func GetSfgInfoKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(SINGLEFLIGHT_INFO, productId, playerId)
}

// GetSfgDeviceKey 单飞PlayerDevice键
func GetSfgDeviceKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(SINGLEFLIGHT_DEVICE, productId, playerId)
}

// GetSfgExtendKey 单飞PlayerExtend键
func GetSfgExtendKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(SINGLEFLIGHT_EXTEND, productId, playerId)
}

// GetSfgPlayerByDeviceKey 根据设备码查询玩家id
func GetSfgPlayerByDeviceKey(productId int32, deviceCode string, loginType commonPB.LOGIN_TYPE) string {
	return fmt.Sprintf(SINGLEFLIGHT_MASTER_PRODUCT_DEVICE, productId, deviceCode, loginType)
}

// GetSfgPlayerByOpenIdKey 根据openId查询玩家id
func GetSfgPlayerByOpenIdKey(productId int32, accType int32, openId string) string {
	return fmt.Sprintf(SINGLEFLIGHT_MASTER_PRODUCT_OPENID, productId, accType, openId)
}

// GetSfgPlayerByAccountKey 根据账号查询玩家id
func GetSfgPlayerByAccountKey(productId int32, account string) string {
	return fmt.Sprintf(SINGLEFLIGHT_MASTER_PRODUCT_ACCOUNT, productId, account)
}
