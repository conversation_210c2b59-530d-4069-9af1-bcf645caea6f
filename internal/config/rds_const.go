package config

import (
	"fmt"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

const (
	// redis key

	// RDS_KEY_IDGEN_SEQUENCE ID 生成器序列键（按产品分区）
	RDS_KEY_IDGEN_SEQUENCE = "idgen:seq:%d"

	// string
	// 玩家游客登录玩家id key
	RDS_KEY_PRODUCT_DEVICE = "product:device:%d:%s:%d"
	// 玩家openId productId:loginType:openId
	RDS_KEY_PLAYER_OPEN_ID = "p_open_id:%d:%d:%s"

	// hash
	// 玩家主信息
	RDS_KEY_PLAYER_MASTER = "p_master:%d:%d"
	// 玩家信息
	RDS_KEY_PLAYER_INFO = "p_info:%d:%d"
	// 玩家设备信息
	RDS_KEY_PLAYER_DEVICE = "p_device:%d:%d"
	// 玩家实名信息
	RDS_KEY_REAL_NAME = "p_rl_name:%d:%d"
	// 账号登录玩家id key
	RDS_KEY_PRODUCT_ACCOUNT = "product:account:%d:%s"
	// 玩家扩展信息
	RDS_KEY_PLAYER_EXTEND = "p_extend:%d:%d"

	// lock
	// 游客登录锁
	RDS_VISITOR_LOGIN_LOCK = "product:device:%d:%s:lock"
	// 账号登录锁
	RDS_ACCOUNT_LOGIN_LOCK = "product:account:%d:%s:lock"
)

// PlayerExpire redis中的玩家信息过期时间 7天加上0到24小时的随机时间
var PLAYER_EXPIRE = 7*24*time.Hour + time.Duration(random.Int64n(int64(time.Hour*24)))

// ID 生成器序列键过期时间
var IDGEN_EXPIRE = 36 * time.Hour

// redis expire
const (
	// 分布式锁时间 60S
	LOCK_EXPIRE = 60 * time.Second
)

// ProductDevicePlayerKey 游客登录唯一标识
func ProductDevicePlayerKey(productId int32, deviceCode string, loginType commonPB.LOGIN_TYPE) string {
	return fmt.Sprintf(RDS_KEY_PRODUCT_DEVICE, productId, deviceCode, loginType)
}

// ProductAccountKey 账号登录唯一标识
func ProductAccountKey(productId int32, account string) string {
	return fmt.Sprintf(RDS_KEY_PRODUCT_ACCOUNT, productId, account)
}

// LockKeyVisitorLogin 游客登录分布式锁key
func LockKeyVisitorLogin(productId int32, deviceCode string) string {
	return fmt.Sprintf(RDS_VISITOR_LOGIN_LOCK, productId, deviceCode)
}

// LockKeyAccountLogin 账号登录分布式锁key
func LockKeyAccountLogin(productId int32, account string) string {
	return fmt.Sprintf(RDS_ACCOUNT_LOGIN_LOCK, productId, account)
}

// PlayerMasterKey redis key
func PlayerMasterKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_MASTER, productId, playerId)
}

// PlayerInfoKey redis key
func PlayerInfoKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_INFO, productId, playerId)
}

// PlayerDevicesKey redis key
func PlayerDevicesKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_DEVICE, productId, playerId)
}

// PlayerOpenID  redis key
func PlayerOpenIDKey(productId int32, accType int32, openId string) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_OPEN_ID, productId, accType, openId)
}

// PlayerExtendKey redis key
func PlayerExtendKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_PLAYER_EXTEND, productId, playerId)
}

// PlayerRealNameKey redis key
func PlayerRealNameKey(productId int32, playerId uint64) string {
	return fmt.Sprintf(RDS_KEY_REAL_NAME, productId, playerId)
}

// IDGenSequenceKey 返回ID生成器的序列key
func IDGenSequenceKey(productId int32) string {
	return fmt.Sprintf(RDS_KEY_IDGEN_SEQUENCE, productId)
}
