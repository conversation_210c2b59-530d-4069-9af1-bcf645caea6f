package daoCreate

import (
	"context"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func Init() {
	db := dict_mysql.MysqlDBPlayer
	confUser := map[string]interface{}{
		"addr":   "localhost:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     db,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		db: confUser,
	})

	conf := map[string]string{
		"addr":   "localhost:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBPlayer: conf,
		dict_redis.RDBLock:   conf,
	})
}


func TestRedis(t *testing.T) {
	Init()
	var err error
	key := "test1"
	client := redisx.GetPlayerCli()
	// pipe := client.Pipeline()
	// pipe.HSet(context.TODO(), key, "vaaa", "www")
	// pipe.RandExpire(context.TODO(), key, 5000000, 5)
	// result, err := pipe.Exec(context.TODO())
	val1, err := client.HSet(context.TODO(), key, "vaaa", "www").Result()
	val2, err := client.RandExpire(context.TODO(), key, 5, 5).Result()
	// t.Logf("test:%+v %+v", result, err)
	t.Logf("finish :%+v %v %v", err, val1, val2)
}
