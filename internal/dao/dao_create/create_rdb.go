package daoCreate

import (
	"context"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
)

// CreatePlayerRdb 创建玩家账号sql
func CreatePlayerRdb(ctx context.Context, pMasterInfo *model.PlayerMaster, playerInfo *model.PlayerInfo, pDeviceInfo *model.PlayerDevices) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	// 玩家信息库
	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	uid, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		// 玩家基础信息
		_, err := session.Table(model.TablePlayerMaster).Insert(pMasterInfo)
		if err != nil {
			errRoll := session.Rollback()
			if errRoll != nil {
				return nil, errRoll
			}
			return 0, err
		}

		playerInfo.PlayerID = pMasterInfo.PlayerID
		// 玩家详细信息
		_, err = session.Table(model.TablePlayerInfo).Insert(playerInfo)
		if err != nil {
			errRoll := session.Rollback()
			if errRoll != nil {
				return nil, errRoll
			}
			return 0, err
		}

		pDeviceInfo.PlayerID = pMasterInfo.PlayerID
		// 玩家设备信息
		_, err = session.Table(model.TablePlayerDevices).Insert(pDeviceInfo)
		if err != nil {
			errRoll := session.Rollback()
			if errRoll != nil {
				return nil, errRoll
			}
			return 0, err
		}

		return pMasterInfo.PlayerID, nil
	})
	if err != nil {
		entry.Errorf("CreatePlayerRdb req:%+v error:%+v", pMasterInfo.PlayerID, err)
		return 0, err
	}

	return uid.(uint64), nil
}
