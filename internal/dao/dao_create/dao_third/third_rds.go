package daoThird

import (
	"context"
	"errors"
	"usersrv/internal/config"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// 第三方登录使用openId
// CachePlayerIdByOpenIdRds 根据产品和openid缓存玩家id
func CachePlayerIdByOpenIdRds(ctx context.Context, productId int32, playerId uint64, accType int32, openId string) error {
	entry := logx.NewLogEntry(ctx)

	rdsKey := config.PlayerOpenIDKey(productId, accType, openId) 

	err := redisx.GetPlayerCli().Set(ctx, rdsKey, playerId, config.PLAYER_EXPIRE).Err()
	if err != nil {
		entry.Errorf("CachePlayerIdByOpenIdRds, key:%s, err:%v", rdsKey, err)
		return err
	}

	return nil
}


// QueryPlayerIdForOpenIdRds 根据openid查询玩家id
func QueryPlayerIdByOpenIdRds(ctx context.Context, productId int32, accType int32, openId string) (uint64, error) {
	entry := logx.NewLogEntry(ctx)

	rdsKey := config.PlayerOpenIDKey(productId, accType, openId)
	playerId, err := redisx.GetPlayerCli().Get(ctx, rdsKey).Uint64()

	if errors.Is(err, redis.Nil) {
		return 0, nil
	} else if err != nil {
		entry.Errorf("query productId:%d, accType:%d, openId:%s, error:%v", productId, accType, openId, err)
		return 0, err
	}

	return playerId, nil
}
