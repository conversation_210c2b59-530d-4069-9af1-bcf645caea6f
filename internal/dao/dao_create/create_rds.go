package daoCreate

import (
	"context"
	"errors"
	"fmt"
	"usersrv/internal/config"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// CachePlayerInfoRds 缓存玩家信息
func CachePlayerInfoRds(ctx context.Context, productId int32, playerId uint64, pMaster *model.PlayerMaster, playerInfo *model.PlayerInfo, pDeviceInfo *model.PlayerDevices) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		return errors.New("player id is empty")
	}

	pipLine := redisx.GetPlayerCli().TxPipeline()

	// 预定义key，避免重复计算
	playerMasterKey := config.PlayerMasterKey(productId, playerId)
	playerInfoKey := config.PlayerInfoKey(productId, playerId)
	playerDevicesKey := config.PlayerDevicesKey(productId, playerId)

	// 玩家基础信息
	if pMaster != nil {
		pipLine.HSet(ctx, playerMasterKey, pMaster.ToRedisHash())
		pipLine.Expire(ctx, playerMasterKey, config.PLAYER_EXPIRE)
	}

	// 玩家详细信息
	if playerInfo != nil {
		pipLine.HSet(ctx, playerInfoKey, playerInfo.ToRedisHash())
		pipLine.Expire(ctx, playerInfoKey, config.PLAYER_EXPIRE)
	}

	// 玩家设备信息
	if pDeviceInfo != nil {
		pipLine.HSet(ctx, playerDevicesKey, pDeviceInfo.ToRedisHash())
		pipLine.Expire(ctx, playerDevicesKey, config.PLAYER_EXPIRE)
	}

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("set player info cache playerId:%d, err:%v", playerId, err)
		return fmt.Errorf("set player info cache error: %w", err)
	}

	entry.Debugf("set playerId:%d success", playerId)

	return nil
}
