package daoAccount

import (
	"context"
	"errors"
	"fmt"
	"usersrv/internal/config"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// QueryPlayerIdForAccountRds 根据账号查询玩家id
func QueryPlayerIdByAccountRds(ctx context.Context, productId int32, account, password string) *model.AccountPlayer {
	entry := logx.NewLogEntry(ctx)
	rdsKey := config.ProductAccountKey(productId, account)
	dataMap, err := redisx.GetPlayerCli().HGetAll(ctx, rdsKey).Result()

	if errors.Is(err, redis.Nil) {
		return nil
	} else if err != nil {
		entry.Errorf("Query info productId:%d, account:%s, error:%v", productId, account, err)
		return nil
	}

	accountPlayer := model.NewAccountPlayerFromRdsHash(dataMap)
	if accountPlayer == nil {
		return nil
	}

	return accountPlayer
}

// CachePlayerIdByAccountRds 根据产品和账号缓存玩家id
func CachePlayerIdByAccountRds(ctx context.Context, productId int32, playerId uint64, account, password string) error {
	entry := logx.NewLogEntry(ctx)
	pipLine := redisx.GetPlayerCli().TxPipeline()
	accountPlayer := model.NewAccountPlayer(playerId, password)
	pipLine.HMSet(ctx, config.ProductAccountKey(productId, account), accountPlayer.ToRedisHash())
	pipLine.Expire(ctx, config.ProductAccountKey(productId, account), config.PLAYER_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("cache player:%d, productId:%d, account:%s, error:%v", playerId, productId, account, err)
		return fmt.Errorf("cache player:%d, error: %v", playerId, err)
	}

	return nil
}
