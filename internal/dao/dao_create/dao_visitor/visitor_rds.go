package daoVisitor

import (
	"context"
	"errors"
	"fmt"
	"usersrv/internal/config"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// GetPlayerIdByDeviceRds 基于device code 查询 playerId
func QueryPlayerIdByDeviceRds(ctx context.Context, productId int32, deviceCode string, loginType commonPB.LOGIN_TYPE) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	rdsKey := config.ProductDevicePlayerKey(productId, deviceCode, loginType)
	playerId, err := redisx.GetPlayerCli().Get(ctx, rdsKey).Uint64()

	if errors.Is(err, redis.Nil) {
		return 0, nil
	} else if err != nil {
		entry.Errorf("query playerIdForDevices productId:%d, deviceCode:%s, error:%v", productId, deviceCode, err)
		return 0, err
	}

	return playerId, nil
}

// CachePlayerIdByDeviceRds 根据产品和设备id缓存玩家id
func CachePlayerIdByDeviceRds(ctx context.Context, productId int32, playerId uint64, deviceCode string, loginType commonPB.LOGIN_TYPE) error {
	entry := logx.NewLogEntry(ctx)
	redisCli := redisx.GetPlayerCli()

	// expire设置过期
	err := redisCli.Set(ctx, config.ProductDevicePlayerKey(productId, deviceCode, loginType), playerId, config.PLAYER_EXPIRE).Err()
	if err != nil {
		entry.Errorf("cache player:%d, productId:%d, deviceCode:%s, error:%v", playerId, productId, deviceCode, err)
		return fmt.Errorf("cache player:%d, error: %w", playerId, err)
	}

	return nil
}
