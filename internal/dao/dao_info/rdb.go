package dao_info

import (
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func load(productId int32, playerId uint64) (*model.PlayerInfo, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  playerId,
		dict.SysWordProductID: productId,
	})
	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	if session == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}
	bean := &model.PlayerInfo{
		ProductID: productId,
		PlayerID:  playerId,
	}
	exist, err := session.Unscoped().Get(bean)
	if err != nil {
		entry.WithError(err).Errorf("get player info fail")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get player info error")
	}
	// 数据不存在
	if !exist {
		return nil, model.PlayerNil
	}

	return bean, nil
}

func update(session *xorm.Session, info *model.PlayerInfo) (int64, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  info.PlayerID,
		dict.SysWordProductID: info.ProductID,
		dict.SysWordChannelID: info.ChannelID,
	})
	entry.Debugf("update p_info:%+v", info)

	bean := &model.PlayerInfo{
		PlayerID: info.PlayerID,
	}
	exist, err := session.Exist(bean)
	if err != nil {
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update info db")
	}
	var aff int64
	if !exist {
		aff, err = session.Insert(info)
		if err != nil {
			entry.WithError(err).Errorf("insert player info fail")
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "insert info db")
		}
	} else {
		aff, err = session.Where("player_id = ? AND product_id = ?", info.PlayerID, info.ProductID).Update(info)
		if err != nil {
			entry.WithError(err).Errorf("update player info fail")
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update info db")
		}
	}

	// 更新不成功
	if aff == 0 {
		entry.Errorf("update player info with no effect")
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update info not affect")
	}

	return aff, nil
}

// BatchByPlayerID 根据uid批量查询
func BatchByPlayerID(productId int32, playerIds []uint64) (map[uint64]*model.PlayerInfo, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordProductID: productId,
	})
	
	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	if session == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}
	
	var beans []*model.PlayerInfo
	err := session.Unscoped().Where("product_id = ?", productId).In("player_id", playerIds).Find(&beans)
	if err != nil {
		entry.WithError(err).Errorf("batch load player info fail")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "load info db")
	}

	// 组合成map
	beansMap := make(map[uint64]*model.PlayerInfo, len(playerIds))
	for _, bean := range beans {
		beansMap[bean.PlayerID] = bean
	}

	return beansMap, nil
}
