package dao_info

import (
	"context"
	"testing"
	"usersrv/internal/model"
	"usersrv/internal/test"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"xorm.io/core"
)

func TestGet(t *testing.T) {
	test.InitSql()
	test.InitRedis()
	ctx := context.Background()
	data, err := Get(ctx, 1, 101)
	if err != nil {
		t.Fatalf("fail:%+v", err)
	}
	t.Logf("data:%+v", data)
}

func TestUpdate(t *testing.T) {
	test.InitSql()
	test.InitRedis()
	ctx := context.Background()
	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	engine.SetLogLevel(core.LOG_INFO)
	engine.ShowSQL(true)
	data := &model.PlayerInfo{
		PlayerID:  9000001,
		ProductID: 2,
	}
	_, err := engine.Transaction(func(s *xorm.Session) (interface{}, error) {
		err := Modify(ctx, s, data)
		return nil, err
	})

	if err != nil {
		t.Fatalf("fail:%+v", err)
	}
	t.Logf("data:%+v", data)
}

func TestSyncTable(t *testing.T) {
	test.InitSql()
	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	engine.Sync2(new(model.PlayerInfo))
}
