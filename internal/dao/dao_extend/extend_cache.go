package daoExtend

import (
	"context"
	"errors"
	"usersrv/internal/config"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"
	"github.com/go-redis/redis/v8"
)

// QueryPlayerExtend 查询playerExtend，并缓存结果，并降级到数据库查询
func QueryPlayerExtend(ctx context.Context, productId int32, playerId uint64) (*model.PlayerExtend, error) {
	sfKey := config.GetSfgExtendKey(productId, playerId)
	val, err := singleflight.DoCtx(ctx, sfKey, func() (interface{}, error) {
		return doGet(ctx, productId, playerId)
	})
	if err != nil {
		return nil, err
	}

	return val.(*model.PlayerExtend), nil
}

// doGet 查询playerDevice，并缓存结果，并降级到数据库查询
func doGet(ctx context.Context, productId int32, playerId uint64) (*model.PlayerExtend, error) {
	cache, err := QueryPlayerExtendRds(ctx, productId, playerId)
	// 已知 有空标记，不需要再查
	if errors.Is(err, redisx.Empty) {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_NOT_EXIST, "player extend not exist")
	}
	// 数据确实没查到，需要降级到数据库查询
	if errors.Is(err, redis.Nil) {
		return downGrade(ctx, productId, playerId)
	}
	// 真有报错
	if err != nil {
		return nil, err
	}

	return cache, nil
}

func downGrade(ctx context.Context, productId int32, playerId uint64) (*model.PlayerExtend, error) {
	data, err := QueryPlayerExtendRdb(ctx, productId, playerId)
	// 真查不到
	if errors.Is(err, model.PlayerNil) {
		key := config.PlayerExtendKey(productId, playerId)
		errSet := redisx.GetPlayerCli().HSetNil(ctx, key)
		if errSet != nil {
			return nil, errSet
		}
		return nil, err
	}
	if err != nil {
		return nil, err
	}
	// 设置缓存
	err = UpdatePlayerExtendRds(ctx, productId, playerId, data)
	if err != nil {
		return nil, err
	}

	return data, nil

}
