package daoExtend

import (
	"context"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/sirupsen/logrus"
)

// QueryPlayerExtendRdb 查询玩家扩展信息
func QueryPlayerExtendRdb(ctx context.Context, productId int32, playerId uint64) (*model.PlayerExtend, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  playerId,
		dict.SysWordProductID: productId,
	})
	
	session, err :=  mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBPlayer)
	if err != nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}

	bean := &model.PlayerExtend{
		ProductID: productId,
		PlayerID:  playerId,
	}

	exist, err := session.Table(model.TablePlayerExtend).Get(bean)
	if err != nil {
		entry.WithError(err).Errorf("get player device fail")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get player device error")
	}
	// 数据不存在
	if !exist {
		return nil, model.PlayerNil
	}

	return bean, nil
}