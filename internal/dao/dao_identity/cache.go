package dao_identity

import (
	"context"
	"errors"
	"time"
	"usersrv/internal/config"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"github.com/ldy105cn/xorm"
)

// Get 查询接口
func Get(ctx context.Context, productId int32, playerId uint64) (*model.TPlayerRealNameAuth, error) {

	cache, err := getCache(ctx, productId, playerId)
	// 已知 有空标记，不需要再查
	if errors.Is(err, redisx.Empty) {
		return nil, err
	}
	// 数据确实没查到，需要降级到数据库查询
	if errors.Is(err, redis.Nil) {
		return downGrade(ctx, productId, playerId)
	}
	// 真有报错
	if err != nil {
		return nil, err
	}

	return cache, nil
}

func downGrade(ctx context.Context, productId int32, playerId uint64) (*model.TPlayerRealNameAuth, error) {
	data, err := load(playerId, productId)
	// 真查不到
	if errors.Is(err, model.PlayerNil) {
		key := config.PlayerRealNameKey(productId, playerId)
		errSet := redisx.GetPlayerCli().HSetNil(ctx, key)
		if errSet != nil {
			return nil, errSet
		}
		return nil, err
	}
	if err != nil {
		return nil, err
	}
	// 设置缓存
	err = setCache(ctx, productId, playerId, data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// 更新修改
func Modify(ctx context.Context, session *xorm.Session, data *model.TPlayerRealNameAuth) error {
	// 该系统只有插入
	if data == nil {
		return protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	err := delCache(ctx, data.ProductId, data.PlayerId)
	if err != nil {
		return err
	}
	// 延迟双删
	time.AfterFunc(3*time.Second, func() {
		ctx := context.Background()
		delCache(ctx, data.ProductId, data.PlayerId)
	})

	err = insert(session, data)
	if err != nil {
		return err
	}

	return nil
}
