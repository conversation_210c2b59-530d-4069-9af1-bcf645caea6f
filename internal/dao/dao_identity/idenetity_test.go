package dao_identity

import (
	"context"
	"testing"
	"usersrv/internal/model"
	"usersrv/internal/test"

	"github.com/sirupsen/logrus"
)

func TestSyncTable(t *testing.T) {
	test.InitSql()
	engine, err := GetMysqlRealNameEngine()
	if engine == nil || err != nil {
		logrus.Errorf("mysql table engine is empty")
		return
	}

	fcmTable := &model.TPlayerRealNameAuth{}
	if err := engine.Sync2(fcmTable); err != nil {
		logrus.Errorf("engine.Sync2(fcmTable) error(%v)", err)
	} else {
		logrus.Infof("sync fcmTable success")
	}
}

func TestUpdate(t *testing.T) {
	test.InitSql()
	test.InitRedis()
	mock := &model.TPlayerRealNameAuth{
		ProductId: 1,
		PlayerId:  888,
		Year:      1995,
		Month:     2,
		Day:       3,
	}

	engine, _ := GetMysqlRealNameEngine()
	session := engine.NewSession()
	session.Begin()
	ctx := context.TODO()
	err := Modify(ctx, session, mock)
	if err != nil {
		t.Fatalf("err:%+v", err)
	}
	session.Commit()
	t.Logf("success")
}

func TestGet(t *testing.T) {
	test.InitSql()
	test.InitRedis()
	ctx := context.TODO()
	info, err := Get(ctx, 1, 888)
	if err != nil {
		t.Fatalf("err:%+v", err)
	}
	t.Logf("info:%+v", info)
}
