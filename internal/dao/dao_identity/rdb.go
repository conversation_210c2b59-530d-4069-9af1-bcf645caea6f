package dao_identity

import (
	"errors"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func GetMysqlRealNameEngine() (*xorm.Engine, error) {
	return mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBPlayer)
}

// Insert 插入注册信息
func insert(session *xorm.Session, info *model.TPlayerRealNameAuth) error {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID: info.PlayerId,
	})
	entry.Debugf("Insert auth info : %d", info.PlayerId)

	// session, _ := getMysqlRealNameEngine()
	_, err := session.Insert(info)
	if err != nil {
		entry.WithError(err).Errorf("insert auth error ")
		return errors.New("insert auth error")
	}
	return nil
}

// Load 查询玩家实名认证信息
func load(playerId uint64, productId int32) (*model.TPlayerRealNameAuth, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID: playerId,
	})
	entry.Debugf("Load auth info : %d", playerId)

	session, _ := GetMysqlRealNameEngine()
	data := &model.TPlayerRealNameAuth{PlayerId: playerId, ProductId: productId}
	exist, err := session.Get(data)
	if err != nil {
		entry.WithError(err).Errorf("get auth error")
		return nil, errors.New("get auth error")
	}
	if !exist {
		return nil, model.PlayerNil
	}
	return data, nil
}

func update(info *model.TPlayerRealNameAuth, fields ...string) (int64, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID: info.PlayerId,
	})
	entry.Infof("Update fcm info : %d", info.PlayerId)

	session, _ := GetMysqlRealNameEngine()

	aff, err := session.Where("player_id = ? ", info.PlayerId).
		Cols(fields...).
		Update(info)
	if err != nil {
		entry.WithError(err).Errorf("update error")
		return 0, err
	}

	if aff == 0 {
		entry.Infof("no date update")
		return 0, nil
	}

	return aff, nil
}
