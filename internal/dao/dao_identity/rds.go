package dao_identity

import (
	"context"
	"usersrv/internal/config"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

func getCache(ctx context.Context, productId int32, playerId uint64) (*model.TPlayerRealNameAuth, error) {
	key := config.PlayerRealNameKey(productId, playerId)

	hashMap, err := redisx.GetPlayerCli().HGetAllWithNil(ctx, key).Result()
	if err != nil {
		return nil, err
	}
	if len(hashMap) == 0 {
		return nil, redis.Nil
	}
	retArr := &model.TPlayerRealNameAuth{}
	err = retArr.FromHash(hashMap)
	if err != nil {
		return nil, err
	}

	return retArr, nil
}

func setCache(ctx context.Context, productId int32, playerId uint64, arr *model.TPlayerRealNameAuth) error {
	entry := logx.NewLogEntry(ctx)
	args, err := arr.ToHash()
	if err != nil {
		return err
	}

	key := config.PlayerRealNameKey(productId, playerId)
	pipe := redisx.GetPlayerCli().Pipeline()

	pipe.HSet(ctx, key, args)
	pipe.Expire(ctx, key, config.PLAYER_EXPIRE)
	_, err = pipe.Exec(ctx)
	if err != nil {
		entry.Error(err)
		return err
	}
	return nil
}

func delCache(ctx context.Context, productId int32, playerId uint64) error {
	key := config.PlayerRealNameKey(productId, playerId)
	err := redisx.GetPlayerCli().Del(ctx, key).Err()
	if err != nil {
		return err
	}
	return nil
}
