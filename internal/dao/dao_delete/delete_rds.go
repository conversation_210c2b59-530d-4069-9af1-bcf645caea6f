package daoDelete

import (
	"context"
	"errors"

	"usersrv/internal/config"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// DeleteAccountRds 删除redis中玩家所有的账户信息
func DeleteAccountRds(ctx context.Context, pMaster *model.PlayerMaster) error {
	entry := logx.NewLogEntry(ctx)
	if pMaster == nil {
		return errors.New("player master is empty")
	}

	playerMasterKey := config.PlayerMasterKey(pMaster.ProductID, pMaster.PlayerID)
	playerInfoKey := config.PlayerInfoKey(pMaster.ProductID, pMaster.PlayerID)
	playerDevicesKey := config.PlayerDevicesKey(pMaster.ProductID, pMaster.PlayerID)
	deviceCodeKey := config.ProductDevicePlayerKey(pMaster.ProductID, pMaster.DeviceCode, pMaster.LoginType)
	accountKey := config.ProductAccountKey(pMaster.ProductID, pMaster.Account)
	openIdKey := config.PlayerOpenIDKey(pMaster.ProductID, int32(pMaster.AccType), pMaster.OpenID)
	extendKey := config.PlayerExtendKey(pMaster.ProductID, pMaster.PlayerID)

	pipLine := redisx.GetPlayerCli().TxPipeline()
	pipLine.Del(ctx, playerMasterKey)
	pipLine.Del(ctx, playerInfoKey)
	pipLine.Del(ctx, playerDevicesKey)
	pipLine.Del(ctx, deviceCodeKey)
	pipLine.Del(ctx, accountKey)
	pipLine.Del(ctx, openIdKey)
	pipLine.Del(ctx, config.GetSfgMasterKey(pMaster.ProductID, pMaster.PlayerID))
	pipLine.Del(ctx, extendKey)

	if _, err := pipLine.Exec(ctx); err != nil {
		entry.Warnf("delete player:%d, pipeline redis error:%s", pMaster.PlayerID, err.Error())
		return err
	}

	entry.Infof("delete player:%d, account:%s success", pMaster.PlayerID, pMaster.Account)

	return nil
}
