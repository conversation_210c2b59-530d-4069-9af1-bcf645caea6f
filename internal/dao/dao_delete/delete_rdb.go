package daoDelete

import (
	"context"
	"errors"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
)

// DeletePlayerRdb 删除账号信息
func DeletePlayerRdb(ctx context.Context, productId int32, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	// 玩家信息库
	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)

	_, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		// 删除玩家基础信息
		_, err := session.Table(model.TablePlayerMaster).Where("product_id = ? and player_id = ?", productId, playerId).Delete(&model.PlayerMaster{})
		if err != nil {
			entry.Errorf("delete player:%d master info, err:%+v", playerId, err)
			return nil, errors.New("delete player master info from mysql error")
		}

		// 删除玩家详细信息
		_, err = session.Table(model.TablePlayerInfo).Where("product_id = ? and player_id = ?", productId, playerId).Delete(&model.PlayerInfo{})
		if err != nil {
			entry.Errorf("delete player:%d player info, err:%+v", playerId, err)
			return nil, errors.New("delete player q info from mysql error")
		}

		// 删除玩家设备信息
		_, err = session.Table(model.TablePlayerDevices).Where("product_id = ? and player_id = ?", productId, playerId).Delete(&model.PlayerDevices{})
		if err != nil {
			entry.Errorf("delete player:%d devices info, err:%+v", playerId, err)
			return nil, errors.New("delete player devices info from mysql error")
		}

		// 删除玩家扩展信息
		_, err = session.Table(model.TablePlayerExtend).Where("product_id = ? and player_id = ?", productId, playerId).Delete(&model.PlayerExtend{})
		if err != nil {
			entry.Errorf("delete player:%d extend info, err:%+v", playerId, err)
			return nil, errors.New("delete player extend info from mysql error")
		}

		return nil, nil
	})

	entry.Infof("delete player info from mysql player:%d, ret:%+v", playerId, err)

	return err
}
