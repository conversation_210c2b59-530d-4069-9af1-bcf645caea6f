package daoQuery

import (
	"context"
	"errors"
	"usersrv/internal/config"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// QueryPlayerMasterInfoRds 查询玩家信息 master和playerInfo
func QueryPlayerMasterInfoRds(ctx context.Context, productId int32, playerId uint64) (*model.PlayerMaster, *model.PlayerInfo, error) {
	entry := logx.NewLogEntry(ctx)
	pipLine := redisx.GetPlayerCli().TxPipeline()

	pipLine.HGetAll(ctx, config.PlayerMasterKey(productId, playerId))
	pipLine.HGetAll(ctx, config.PlayerInfoKey(productId, playerId))

	replies, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Warnf("query player:%d, pipeline redis error:%s", playerId, err.Error())
		return nil, nil, err
	}

	if len(replies) != 2 {
		entry.Warnf("query player:%d, unexpected number of replies", playerId)
		return nil, nil, errors.New("unexpected number of replies")
	}

	pMasterReply := replies[0].(*redis.StringStringMapCmd)
	playerInfoReply := replies[1].(*redis.StringStringMapCmd)

	masterMap, err := pMasterReply.Result()
	if err != nil {
		entry.Warnf("query player:%d, masterPlayer info error:%s", playerId, err.Error())
		return nil, nil, err
	}

	playerMap, err := playerInfoReply.Result()
	if err != nil {
		entry.Warnf("query player:%d, playerInfo info error:%s", playerId, err.Error())
		return nil, nil, err
	}

	pMaster := model.NewPlayerMasterFromRdsHash(masterMap)
	playerInfo := model.NewPlayerInfoFromRdsHash(playerMap)

	if pMaster == nil || playerInfo == nil {
		entry.Warnf("query player:%d, masterPlayer or playerInfo is nil", playerId)
		return nil, nil, errors.New("masterPlayer or playerInfo is nil")
	}

	// 加上额外处理 playerId为0的情况
	if pMaster.PlayerID == 0 || playerInfo.PlayerID == 0 {
		entry.Warnf("query player:%d, masterPlayer or playerInfo is nil", playerId)
		return nil, nil, errors.New("masterPlayer or playerInfo is nil")
	}

	entry.Debugf("query player:%d, masterPlayer:%v, playerInfo:%v", playerId, *pMaster, *playerInfo)

	return pMaster, playerInfo, nil
}

// QueryPlayerDeviceInfoRds 查询玩家设备信息
func QueryPlayerDeviceInfoRds(ctx context.Context, productId int32, playerId uint64) (*model.PlayerDevices, error) {
	entry := logx.NewLogEntry(ctx)
	rdsHash, err := redisx.GetPlayerCli().HGetAll(ctx, config.PlayerDevicesKey(productId, playerId)).Result()
	if err != nil {
		entry.Warnf("QueryPlayerDeviceInfoRds, player:%d, redis error:%s", playerId, err.Error())
		return nil, err
	}

	playerDevice := model.NewDeviceInfoFromRdsHash(rdsHash)
	if playerDevice == nil {
		entry.Warnf("QueryPlayerDeviceInfoRds, player:%d, device is nil", playerId)
		return nil, errors.New("device is nil")
	}

	entry.Debugf("query player device info, player:%d, device:%v", playerId, *playerDevice)

	return playerDevice, nil
}
