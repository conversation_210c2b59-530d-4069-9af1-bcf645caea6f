package daoQuery

import (
	"context"
	"sync"
	"testing"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func Init() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     dict_mysql.MysqlDBPlayer,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})
}

func TestPlayerId(t *testing.T) {
	Init()
	querlplayerId, _ := QueryPlayerIdForFiledRdb(context.Background(), model.TablePlayerMaster, map[string]interface{}{
		"product_id":1,
		"device_code": "900000000006",
	})
	logrus.Infof("playerId:%d", querlplayerId)
}

func TestConcurrenceNoSF(t *testing.T) {
	var wg sync.WaitGroup

	Init()
	wg.Add(10)
	for i := 0; i < 10; i++ {
		go func() {
			defer wg.Done()

			pm, pi, err := QueryPlayerMasterInfoRdb(context.Background(), 1, 20603)
			if err != nil {
				t.Errorf("err : %+v", err)
			}
			t.Logf("QueryInfo pm:%+v pv:%+v", pm.PlayerID, pi.PlayerID)
		}()
	}
	wg.Wait()
}

func TestConcurrenceSF(t *testing.T) {
	var wg sync.WaitGroup

	Init()
	wg.Add(10)
	for i := 0; i < 10; i++ {
		go func() {
			defer wg.Done()

			key := "tc_singleflight"
			v, err, _ := singleflight.Do(key, func() (interface{}, error) {
				pm, _, err := QueryPlayerMasterInfoRdb(context.Background(), 1, 20603)
				if err != nil {
					t.Errorf("err : %+v", err)
				}
				// t.Logf("QueryInfo pm:%+v pv:%+v", pm.PlayerID, pi.PlayerID)
				return pm, nil
			})
			if err != nil {
				t.Error(err)
			}
			t.Log(v)
		}()
	}
	wg.Wait()
}
