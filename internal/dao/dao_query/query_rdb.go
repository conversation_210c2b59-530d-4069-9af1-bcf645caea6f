package daoQuery

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"usersrv/internal/config"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// QueryPlayerIdForFiledRdb 根据字段查询玩家信息 区分表单 针对单表查询
func  QueryPlayerIdForFiledRdb(ctx context.Context, tableName string, fieldValues map[string]interface{}) (uint64, error) {
	entry := logx.NewLogEntry(ctx)
	// 使用 singleflight 防止并发查询数据库
	cacheKey := transform.MapStrInterfaceToString(fieldValues, "_")
	key := config.GetPlayerIdBySingleflight(cacheKey)

	if tableName == "" {
		tableName = model.TablePlayerMaster
	}

	value, err, _ := singleflight.Do(key, func() (interface{}, error) {
		var queryBuilder strings.Builder
		values := make([]interface{}, 0, len(fieldValues))
		for field, value := range fieldValues {
			if queryBuilder.Len() > 0 {
				queryBuilder.WriteString(" AND ")
			}
			queryBuilder.WriteString("`")
			queryBuilder.WriteString(field)
			queryBuilder.WriteString("` = ?")
			values = append(values, value)
		}
		queryBuilder.WriteString(" AND deleted_at is null")

		// 减少读取内容和解析压力
		var data uint64
		session := mysql.GetMysqlTableEngine(dict_mysql.MysqlDBPlayer, tableName)
		ok, err := session.Where(queryBuilder.String(), values...).
			Cols("player_id").
			Desc("update_time").
			Limit(1, 0).
			Get(&data)

		if err != nil {
			sql, params := session.LastSQL()
			entry.Errorf("query fieldValues:%v, sql:%v, params:%v, error:%v", fieldValues, sql, params, err)
			return nil, fmt.Errorf("SQL execution error: %w", err)
		}

		if !ok {
			entry.Warnf("query fieldValues:%v not found", fieldValues)
			return uint64(0), nil
		}
		return data, nil
	})

	if err != nil {
		entry.Errorf("get player id error, cacheKey:%s, err:%v", cacheKey, err)
		return 0, fmt.Errorf("failed to get player ID: %w", err)
	}

	playerId, ok := value.(uint64)
	if !ok || playerId <= 0 {
		entry.Warnf("GetPlayerIDBy, fieldValues:%v, playerId:%d", fieldValues, playerId)
		return 0, model.PlayerNil
	}

	entry.Debugf("get player id success, fieldValues:%v player:%d", fieldValues, playerId)
	
	return playerId, nil
}

// QueryPlayerMasterInfoRdb 查询数据库玩家信息 包含基础信息和详细信息
func QueryPlayerMasterInfoRdb(ctx context.Context, productId int32, playerId uint64) (*model.PlayerMaster, *model.PlayerInfo, error) {
	// 使用 singleflight 防止并发查询数据库
	entry := logx.NewLogEntry(ctx)
	pMaster := &model.PlayerMaster{}
	playerInfo := &model.PlayerInfo{}

	// 玩家信息库
	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)

	// 查询玩家基础信息
	is_exist, err := session.Table(model.TablePlayerMaster).Where("product_id = ? and player_id = ?", productId, playerId).Get(pMaster)
	if !is_exist || err != nil {
		entry.Errorf("failed to retrieve player basic info for playerId: %d, is_exist: %+v, err:%+v", playerId, is_exist, err)
		return nil, nil, errors.New("get player basic info from mysql error")
	}

	// 查询玩家详细信息
	is_exist, err = session.Table(model.TablePlayerInfo).Where("product_id = ? and player_id = ?", productId, playerId).Get(playerInfo)
	if !is_exist || err != nil {
		entry.Errorf("failed to retrieve player detailed info for playerId: %d, is_exist: %+v, err:%+v", playerId, is_exist, err)
		return nil, nil, errors.New("get player detailed info from mysql error")
	}

	if playerInfo.PlayerID <= 0 || pMaster.PlayerID <= 0 {
		entry.Errorf("failed to retrieve playerId: %d error, playerInfo:%+v, masterInfo:%+v", playerId, playerInfo, pMaster)
		return nil, nil, errors.New("get player detailed info from mysql error")
	}

	entry.Infof("get player info from mysql, product:%d, player:%d, pMaster:%v, playerInfo:%v", productId, playerId, pMaster, playerInfo)

	return pMaster, playerInfo, nil
}
