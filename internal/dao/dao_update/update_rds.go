package daoUpdate

import (
	"context"
	"errors"
	"usersrv/internal/config"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// UpdatePlayerForFiledRds 更新玩家id到redis中
func UpdatePlayerForFiledRds(ctx context.Context, playerId uint64, rdsKey string) error {
	entry := logx.NewLogEntry(ctx)
	_, err := redisx.GetPlayerCli().Set(ctx, rdsKey, playerId, config.PLAYER_EXPIRE).Result()
	if err != nil {
		entry.Errorf("UpdatePlayerForFiledRds, key:%s, err:%v", rdsKey, err)
	}

	return err
}

// DelPlayerRds 删除redis缓存
func DelPlayerRds(ctx context.Context, playerId uint64, redisKey string) error {
	redisCli := redisx.GetPlayerCli()
	entry := logx.NewLogEntry(ctx)
	err := redisCli.Del(ctx, redisKey).Err()
	if err != nil {
		entry.Errorf("delete player:%d redis key:%s, error:%v", playerId, redisKey, err)
	}

	return err
}

// UpdatePlayerDeviceRds 更新玩家设备信息
func UpdatePlayerDeviceRds(ctx context.Context, playerId uint64, playerDevice *model.PlayerDevices) error {
	entry := logx.NewLogEntry(ctx)
	if playerDevice == nil || playerId == 0 {
		entry.Errorf("UpdatePlayerDeviceRds, playerId:%d, playerDevice:%v", playerId, playerDevice)
		return errors.New("param error")
	}

	// 将map转换为Redis命令所需的参数列表
	// values := make([]interface{}, 0, len(playerDevice.ToRedisHash())*2)
	// for k, v := range playerDevice.ToRedisHash() {
	// 	values = append(values, k, v)
	// }

	pipLine := redisx.GetPlayerCli().TxPipeline()

	// 玩家设备信息
	// pipLine.HMSet(ctx, model.PlayerDevicesKey(playerId), values...)
	pipLine.HMSet(ctx, config.PlayerDevicesKey(playerDevice.ProductID, playerId), playerDevice)
	// 设置超时时间
	pipLine.Expire(ctx, config.PlayerDevicesKey(playerDevice.ProductID, playerId), config.PLAYER_EXPIRE)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("UpdatePlayerDeviceRds, playerId:%d, error:%v", playerId, err)
	}

	return err
}
