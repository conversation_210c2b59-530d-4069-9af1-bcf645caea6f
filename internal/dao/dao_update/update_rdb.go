package daoUpdate

import (
	"context"
	"reflect"

	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/reflectext"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

// UpdateInfoByFieldRdb 更新指定库玩家信息
func UpdateInfoByFieldRdb(ctx context.Context, session *xorm.Session, productId int32,  playerId uint64, tableName string, updateInfo map[string]interface{}) (int64, error) {
	// 初始化日志记录
	entry := logrus.WithFields(logrus.Fields{
		"product_id": productId,
		"player_id":  playerId,
		"table":      tableName,
		"update_info": updateInfo,
	})
	
	// 获取结构体类型
	structType, found := model.TableStructMap[tableName]
	if !found {
		// 如果未找到对应的结构体类型，返回错误
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "invalid table name")
	}

	// 确保 structType 是指针类型
	if structType.Kind() != reflect.Ptr {
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "invalid struct type")
	}

	// 创建查询结构体实例 只包含 player_id 和 product_id 字段
	bean := reflect.New(structType.Elem()).Interface()
	v := reflect.ValueOf(bean).Elem()

	// 设置product_id和player_id
	v.FieldByName("PlayerID").Set(reflect.ValueOf(playerId))
	v.FieldByName("ProductID").Set(reflect.ValueOf(productId))
	
	// 查询是否存在 这种方式会考虑deleteAt是否有效
	exist, err := session.Exist(bean)
	if err != nil {
		// 如果检查记录存在时出错，记录错误并返回错误
		entry.WithError(err).Errorf("check existence fail")
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "check existence error")
	}

	// 更新信息中添加 player_id 和 product_id 字段
	updateInfo["player_id"] = playerId
	updateInfo["product_id"] = productId

	// 获取 json 标签到结构体字段名的映射
	jsonToStructFieldMap := reflectext.GetJSONToStructFieldMap(structType)

	// 这里只做校验 判断jsonFieldName是否在structType中存在
	for jsonFieldName := range updateInfo {
		structFieldName, found := jsonToStructFieldMap[jsonFieldName]
        if !found {
            // 如果找不到对应的结构体字段名，返回错误
            return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "invalid field name")
        }

        _, found = structType.Elem().FieldByName(structFieldName)
        if !found {
            // 如果找不到对应的结构体字段，返回错误
            return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "invalid field name")
        }

        // // 将字符串转换为字段所需的类型
        // fieldValue := reflect.ValueOf(bean).Elem().FieldByName(structFieldName)
        // newValueValue := reflect.ValueOf(newValue)
        // if !newValueValue.Type().ConvertibleTo(field.Type) {
        //     return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "invalid value type")
        // }

        // // 设置字段值
        // fieldValue.Set(newValueValue.Convert(field.Type))
        // filedNameList = append(filedNameList, jsonFieldName)
	}

	var aff int64
	if !exist {
		// 插入新记录
		_, err = session.Table(tableName).Insert(updateInfo)
		if err != nil {
			errRoll := session.Rollback()
			if errRoll != nil {
				entry.WithError(errRoll).Errorf("insert extend rollback info fail")
				return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "insert rollback info error")
			}
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "insert info error")
		}
	} else {
		// 更新现有记录
		aff, err = session.Table(tableName).Where("player_id = ? AND product_id = ?", playerId, productId).Update(updateInfo)
		if err != nil {
			errRoll := session.Rollback()
			if errRoll != nil {
				entry.WithError(errRoll).Errorf("update info rollback info fail")
				return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update info rollback info error")
			}
			entry.WithError(err).Errorf("update info fail")
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update info error")
		}
	}


	// 更新不成功
	if aff == 0 {  
		entry.Warnf("update info with no effect")
		return 0, nil
	}

	return aff, nil
}
