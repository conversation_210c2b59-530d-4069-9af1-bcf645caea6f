package daoUpdate

import (
	"context"
	"testing"
	"usersrv/internal/model"
	"usersrv/internal/test"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"xorm.io/core"
)

func TestUpdate(t *testing.T) {
	test.InitSql()
	ctx := context.TODO()
	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	updateData := make(map[string]interface{})
	var playerId uint64 = 999999
	updateData["nick_name"] = "test4"
	updateData["sex"] = "4"

	engine.Logger().SetLevel(core.LOG_DEBUG)
	engine.Logger().ShowSQL(true)

	_, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		return UpdateInfoByFieldRdb(ctx, session, 1, playerId, model.TablePlayerInfo, updateData)
	},
	)
	if err != nil {
		t.Fatalf("failed to update player info:%+v", err)
	}
}
