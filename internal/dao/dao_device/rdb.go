package dao_device

import (
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func load(productId int32, playerId uint64) (*model.PlayerDevices, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  playerId,
		dict.SysWordProductID: productId,
	})
	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	if session == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}
	bean := &model.PlayerDevices{
		ProductID: productId,
		PlayerID:  playerId,
	}
	exist, err := session.Get(bean)
	if err != nil {
		entry.WithError(err).Errorf("get player device fail")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get player device error")
	}
	// 数据不存在
	if !exist {
		return nil, model.PlayerNil
	}

	return bean, nil
}

func update(session *xorm.Session, info *model.PlayerDevices) (int64, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  info.PlayerID,
		dict.SysWordProductID: info.ProductID,
	})
	entry.Debugf("update p_device:%+v", info)

	bean := &model.PlayerDevices{
		PlayerID: info.PlayerID,
	}
	exist, err := session.Exist(bean)
	if err != nil {
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update device db")
	}
	var aff int64
	if !exist {
		aff, err = session.Insert(info)
		if err != nil {
			entry.WithError(err).Errorf("insert player device fail")
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "insert device db")
		}
	} else {
		aff, err = session.Where("player_id = ? AND product_id = ?", info.PlayerID, info.ProductID).Update(info)
		if err != nil {
			entry.WithError(err).Errorf("update player device fail")
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update device db")
		}
	}

	// 更新不成功
	if aff == 0 {
		entry.Errorf("update player device with no effect")
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update device not affect")
	}

	return aff, nil
}