package dao_device

import (
	"context"
	"errors"
	"usersrv/internal/config"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// 获取Rds缓存
func getCache(ctx context.Context, productId int32, playerId uint64) (*model.PlayerDevices, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  playerId,
		dict.SysWordProductID: productId,
	})
	key := config.PlayerDevicesKey(productId, playerId)

	hashMap, err := redisx.GetPlayerCli().HGetAllWithNil(ctx, key).Result()
	if errors.Is(err, redisx.Empty) {
		return nil, redis.Nil
	}
	if err != nil {
		entry.Warnf("redis get err: %v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "p_device rds")
	}
	if len(hashMap) == 0 {
		return nil, redis.Nil
	}
	ret := model.NewDeviceInfoFromRdsHash(hashMap)

	return ret, nil
}

func setCache(ctx context.Context, productId int32, playerId uint64, playerDevice *model.PlayerDevices) error {
	entry := logx.NewLogEntry(ctx)
	key := config.PlayerDevicesKey(productId, playerId)
	args := playerDevice.ToRedisHash()
	pipe := redisx.GetPlayerCli().Pipeline()
	pipe.HSet(ctx, key, args)
	pipe.Expire(ctx, key, config.PLAYER_EXPIRE)
	_, err := pipe.Exec(ctx)
	if err != nil {
		entry.WithError(err).Errorf("p_device setCache error")
		return err
	}
	return nil
}

// delCache 删除缓存
func delCache(ctx context.Context, productId int32, playerId uint64) error {
	key := config.PlayerDevicesKey(productId, playerId)
	err := redisx.GetPlayerCli().Del(ctx, key).Err()
	if err != nil {
		return err
	}
	return nil
}
