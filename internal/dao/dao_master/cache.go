package dao_master

import (
	"context"
	"errors"
	"time"
	"usersrv/internal/config"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"
	"github.com/go-redis/redis/v8"
	"github.com/ldy105cn/xorm"
)

// Get 查询playerMaster
func Get(ctx context.Context, productId int32, playerId uint64) (*model.PlayerMaster, error) {
	sfKey := config.GetSfgMasterKey(productId, playerId)
	val, err := singleflight.DoCtx(ctx, sfKey, func() (interface{}, error) {
		return doGet(ctx, productId, playerId)
	})
	if err != nil {
		return nil, err
	}

	return val.(*model.PlayerMaster), nil
}

// 查询数据
func doGet(ctx context.Context, productId int32, playerId uint64) (*model.PlayerMaster, error) {
	cache, err := getCache(ctx, productId, playerId)
	// 已知 有空标记，不需要再查
	if errors.Is(err, redisx.Empty) {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_NOT_EXIST, "player master not exist")
	}
	// 数据确实没查到，需要降级到数据库查询
	if errors.Is(err, redis.Nil) {
		return downGrade(ctx, productId, playerId)
	}
	// 真有报错
	if err != nil {
		return nil, err
	}

	return cache, nil
}

// 降级查询
func downGrade(ctx context.Context, productId int32, playerId uint64) (*model.PlayerMaster, error) {
	data, err := load(productId, playerId)
	// 真查不到
	if errors.Is(err, model.PlayerNil) {
		key := config.PlayerMasterKey(productId, playerId)
		errSet := redisx.GetPlayerCli().HSetNil(ctx, key)
		if errSet != nil {
			return nil, errSet
		}
		return nil, err
	}
	if err != nil {
		return nil, err 
	}
	// 设置缓存
	err = setCache(ctx, productId, playerId, data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// Modify 更新修改
func Modify(ctx context.Context, session *xorm.Session, data *model.PlayerMaster) error {
	if data == nil {
		return protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}
	err := delCache(ctx, data.ProductID, data.PlayerID)
	if err != nil {
		return protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}
	// 延迟双删
	time.AfterFunc(3*time.Second, func() {
		ctx := context.Background()
		delCache(ctx, data.ProductID, data.PlayerID)
	})
	_, err = update(session, data)
	if err != nil {
		return protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	return nil
}

// BatchGet 批量查询playerMaster
func BatchGet(ctx context.Context, productId int32, playerIds []uint64) (map[uint64]*model.PlayerMaster, error) {
	return BatchByPlayerID(productId, playerIds)
}
