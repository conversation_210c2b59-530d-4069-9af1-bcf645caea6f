package dao_master

import (
	"context"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

func load(productId int32, playerId uint64) (*model.PlayerMaster, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  playerId,
		dict.SysWordProductID: productId,
	})
	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	if session == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}
	bean := &model.PlayerMaster{
		ProductID: productId,
		PlayerID:  playerId,
	}
	exist, err := session.Get(bean)
	if err != nil {
		entry.WithError(err).Errorf("get player master fail")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get player master error")
	}
	// 数据不存在
	if !exist {
		return nil, model.PlayerNil
	}

	return bean, nil
}

func update(session *xorm.Session, info *model.PlayerMaster) (int64, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordPlayerID:  info.PlayerID,
		dict.SysWordProductID: info.ProductID,
		dict.SysWordChannelID: info.ChannelID,
	})
	entry.Debugf("update p_master:%+v", info)

	bean := &model.PlayerMaster{
		PlayerID: info.PlayerID,
	}
	exist, err := session.Exist(bean)
	if err != nil {
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update master db")
	}
	var aff int64
	if !exist {
		aff, err = session.Insert(info)
		if err != nil {
			entry.WithError(err).Errorf("insert player master fail")
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "insert master db")
		}
	} else {
		aff, err = session.Where("player_id = ? AND product_id = ?", info.PlayerID, info.ProductID).Update(info)
		if err != nil {
			entry.WithError(err).Errorf("update player master fail")
			return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update master db")
		}
	}

	// 更新不成功
	if aff == 0 {
		entry.Errorf("update player master with no effect")
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "update master not affect")
	}

	return aff, nil
}

// 分页获取 player_id
func Batch(productId int32, req *commonPB.PaginationReq) ([]uint64, int64, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordProductID: productId,
	})

	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	if session == nil {
		return nil, 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}

	var count int64
	count, err := session.
		Where("product_id = ?", productId).
		Count(&model.PlayerMaster{})

	if err != nil {
		entry.WithError(err).Errorf("get player count master fail")
		return nil, 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get player master count error")
	}
	dbRes := make([]uint64, 0, req.PageSize)
	err = session.Table(&model.PlayerMaster{}).
		Cols("player_id").
		Where("product_id = ? ", productId).
		OrderBy("last_login_time desc").
		Limit(int(req.PageSize), int((req.PageIndex-1)*req.GetPageSize())).
		Find(&dbRes)
	if err != nil {
		entry.WithError(err).Errorf("get player master fail")
		return nil, 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get player master error")
	}

	return dbRes, count, nil
}

// BatchByPlayerID 根据uid批量查询
func BatchByPlayerID(productId int32, playerIds []uint64) (map[uint64]*model.PlayerMaster, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordProductID: productId,
	})

	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	if session == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}

	// 查询所有数据
	var dbRes []*model.PlayerMaster
	err := session.Table(&model.PlayerMaster{}).Unscoped().Where("product_id = ?", productId).In("player_id", playerIds).Find(&dbRes)
	if err != nil {
		entry.WithError(err).Errorf("get player master fail")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get player master error")
	}

	// 组合成map类型
	dbResMap := make(map[uint64]*model.PlayerMaster, len(dbRes))
	for _, v := range dbRes {
		dbResMap[v.PlayerID] = v
	}

	return dbResMap, nil
}

// GetMaxPlayerID 获取最大player_id
func GetMaxPlayerID(ctx context.Context, productId int32) (uint64, error) {
	entry := logrus.WithFields(logrus.Fields{
		dict.SysWordProductID: productId,
	})

	session := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	if session == nil {
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get mysql engine error")
	}

	var maxPlayerID uint64
	_, err := session.Table(&model.PlayerMaster{}).
		Cols("MAX(player_id) as player_id").
		Where("product_id = ?", productId).
		Get(&maxPlayerID)

	if err != nil {
		entry.WithError(err).Errorf("get max player_id fail")
		return 0, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "query max player_id error")
	}

	return maxPlayerID, nil
}
