package rpc

import (
	"context"
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"sync"
)

type GmService struct{}

var (
	gmOnce              = &sync.Once{}
	gmSingletonInstance *GmService
)

func GetGmInstance() *GmService {
	if gmSingletonInstance != nil {
		return gmSingletonInstance
	}
	gmOnce.Do(func() {
		gmSingletonInstance = &GmService{}
	})
	return gmSingletonInstance
}

func (g *GmService) BatchPlayerInfo(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:BatchPlayerInfo] req:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}

	gmReq := &userRpc.BatchPlayerInfoReq{}
	err := json.Unmarshal([]byte(req.Data), gmReq)
	if err != nil {
		return rsp, err
	}

	server := UserServiceServer{}
	resp, err := server.BatchPlayerInfo(ctx, gmReq)
	if err != nil {
		return nil, err
	}

	entry.Infof("[gm:BatchPlayerInfo] resp:%s", resp.String())
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	data, err := json.Marshal(resp)
	if err != nil {
		return rsp, err
	}

	rsp.Data = string(data)

	return rsp, nil
}
