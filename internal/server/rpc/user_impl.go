package rpc

import (
	"context"
	"usersrv/internal/services"

	"git.keepfancy.xyz/back-end/frameworks/kit/rpc"

	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
)

type UserServiceServer struct{}

// CreatePlayer 创建玩家
func (u *UserServiceServer) CreatePlayer(ctx context.Context, req *userRpc.CreatePlayerReq) (*userRpc.CreatePlayerRsp, error) {
	return services.CreatePlayer(ctx, req)
}

// GetPlayerInfo 查询玩家信息
func (u *UserServiceServer) GetPlayerInfo(ctx context.Context, req *userRpc.GetPlayerInfoReq) (*userRpc.GetPlayerInfoRsp, error) {
	return services.GetPlayerInfoByUid(ctx, req)
}

// GetPlayerIdByDevice 根据产品id和设备码查询玩家id
func (u *UserServiceServer) GetPlayerIdByDevice(cxt context.Context, req *userRpc.GetPlayerIdByDeviceReq) (*userRpc.GetPlayerIdByDeviceRsp, error) {
	return services.GetPlayerIdByDevice(cxt, req)
}

// UpdatePlayerInfo 更新玩家信息
func (u *UserServiceServer) UpdatePlayerInfo(ctx context.Context, req *userRpc.UpdatePlayerInfoReq) (*userRpc.UpdatePlayerInfoRsp, error) {
	return services.UpdatePlayerInfoById(ctx, req)
}

// GetPlayerDeviceInfo 查询玩家设备信息
func (u *UserServiceServer) GetPlayerDeviceInfo(ctx context.Context, req *userRpc.GetPlayerDeviceInfoReq) (*userRpc.GetPlayerDeviceInfoRsp, error) {
	return services.GetPlayerDeviceInfoById(ctx, req)
}

// GetPlayerIdByAccount 根据账号查询玩家id
func (u *UserServiceServer) GetPlayerIdByAccount(ctx context.Context, req *userRpc.GetPlayerIdByAccountReq) (*userRpc.GetPlayerIdByAccountRsp, error) {
	return services.GetPlayerIdByAccount(ctx, req)
}

// DeleteAccount 注销账号
func (u *UserServiceServer) DeleteAccount(ctx context.Context, req *userRpc.DeleteAccountReq) (*userRpc.DeleteAccountRsp, error) {
	return services.DeleteAccount(ctx, req)
}

// RealNameAuthQuery 实名认证查询
func (u *UserServiceServer) RealNameAuthQuery(ctx context.Context, req *userRpc.RealNameAuthQueryReq) (*userRpc.RealNameAuthQueryRsp, error) {
	return services.RealNameAuthQuery(ctx, req)
}

// RealNameAuth 实名认证
func (u *UserServiceServer) RealNameAuth(ctx context.Context, req *userRpc.RealNameAuthReq) (*userRpc.RealNameAuthRsp, error) {
	return services.RealNameAuthUpdate(ctx, req)
}

// PlayerAgeQuery 查询用户年龄段
func (u *UserServiceServer) PlayerAgeQuery(ctx context.Context, req *userRpc.PlayerAgeQueryReq) (*userRpc.PlayerAgeQueryRsp, error) {
	return services.PlayerAgeQuery(ctx, req)
}

// PlayerMultiQuery 批量查询用户信息
func (u *UserServiceServer) PlayerMultiQuery(ctx context.Context, req *userRpc.PlayerMultiQueryReq) (*userRpc.PlayerMultiQueryRsp, error) {
	return services.PlayerMultiQuery(ctx, req)
}

// UpdatePlayerLogin 更新玩家登录信息
func (u *UserServiceServer) UpdatePlayerLogin(ctx context.Context, req *userRpc.UpdateLoginReq) (*userRpc.UpdateLoginRsp, error) {
	return services.PlayerLoginUpdate(ctx, req)
}

// BatchPlayerInfo 批量获取玩家全量信息
func (u *UserServiceServer) BatchPlayerInfo(ctx context.Context, req *userRpc.BatchPlayerInfoReq) (*userRpc.BatchPlayerInfoRsp, error) {
	return services.BatchPlayerInfo(ctx, req)
}

// GetPlayerIdByOpenId 根据openId查询玩家id
func (u *UserServiceServer) GetPlayerIdByOpenId(ctx context.Context, req *userRpc.GetPlayerIdByOpenIdReq) (*userRpc.GetPlayerIdByOpenIdRsp, error) {
	return services.GetPlayerIdByOpenId(ctx, req)
}

// QueryPlayerExtendInfo 查询玩家扩展信息
func (u *UserServiceServer) QueryPlayerExtendInfo(ctx context.Context, req *userRpc.QueryPlayerExtendInfoReq) (*userRpc.QueryPlayerExtendInfoRsp, error) {
	return services.QueryPlayerExtendInfo(ctx, req)
}

// UpdatePlayerExtendInfo 更新玩家扩展信息
func (u *UserServiceServer) UpdatePlayerExtendInfo(ctx context.Context, req *userRpc.UpdatePlayerExtendInfoReq) (*userRpc.UpdatePlayerExtendInfoRsp, error) {
	return services.UpdatePlayerExtendInfo(ctx, req)
}

// BatchPlayerBriefInfo 批量查询玩家简要信息
func (u *UserServiceServer) BatchPlayerBriefInfo(ctx context.Context, req *userRpc.BatchPlayerBriefInfoReq) (*userRpc.BatchPlayerBriefInfoRsp, error) {
	return services.BatchPlayerBriefInfo(ctx, req)
}

func InitUserRpc() {
	userRpcService := &UserServiceServer{}
	userRpc.RegisterUserServiceServer(rpc.Server, userRpcService)
}
