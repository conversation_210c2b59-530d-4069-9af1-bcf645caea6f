package logicCommon

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// ConvertLoginTypeToAccType 登录类型转换为账号类型
func ConvertLoginTypeToAccType(loginType commonPB.LOGIN_TYPE) commonPB.ACC_TYPE {
	switch loginType {
	case commonPB.LOGIN_TYPE_LT_VISITOR:
		return commonPB.ACC_TYPE_AT_VISITOR
	case commonPB.LOGIN_TYPE_LT_TOKEN:
		return commonPB.ACC_TYPE_AT_TOKEN
	case commonPB.LOGIN_TYPE_LT_FACEBOOK:
		return commonPB.ACC_TYPE_AT_FACEBOOK
	case commonPB.LOGIN_TYPE_LT_APPLE:
		return commonPB.ACC_TYPE_AT_APPLE
	case commonPB.LOGIN_TYPE_LT_PASSWORD:
		return commonPB.ACC_TYPE_AT_PASSWORD
	case commonPB.LOGIN_TYPE_LT_GOOGLE:
		return commonPB.ACC_TYPE_AT_GOOGLE
	}

	return commonPB.ACC_TYPE_AT_INIT
}

// IsThirdLoginType 是否是第三方登录
func IsThirdLoginType(accType commonPB.ACC_TYPE) bool {
	return accType >= commonPB.ACC_TYPE_AT_FACEBOOK && accType <= commonPB.ACC_TYPE_AT_THIRD_END
}
