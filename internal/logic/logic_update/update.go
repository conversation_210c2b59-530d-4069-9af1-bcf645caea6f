package logicUpdate

import (
	"context"
	"time"
	"usersrv/internal/config"
	daoUpdate "usersrv/internal/dao/dao_update"
	"usersrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
)

// UpdatePlayerInfoDynamic 根据参数动态更新玩家信息 可以分表处理 需要传入对应的tableName和rdsKey
func UpdatePlayerInfoDynamic(ctx context.Context, productId int32, playerId uint64, rdsKey string, tableName string, updateInfo map[string]interface{}) error {
	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	entry := logx.NewLogEntry(ctx)
	_, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		var row int64
		var err error

		// 玩家详细信息
		if len(updateInfo) > 0 {
			// 先删除redis中的玩家信息
			daoUpdate.DelPlayerRds(ctx, playerId, rdsKey)

			// 延时双删
			time.AfterFunc(time.Second*3, func() {
				// error:context canceled
				ctx := context.Background()
				daoUpdate.DelPlayerRds(ctx, playerId, rdsKey)
			})

			// 更新到数据库
			row, err := daoUpdate.UpdateInfoByFieldRdb(ctx, session, productId, playerId, tableName, updateInfo)
			if err != nil {
				return row, err
			}
		}

		return row, err
	})

	if err != nil {
		entry.Errorf("update player:%d tableName:%s, rdsKey:%s, info:%+v error:%v", playerId, tableName, rdsKey, updateInfo, err)
		return err
	}

	entry.Infof("update player:%d tableName:%s, rdsKey:%s, info:%+v success", playerId, tableName, rdsKey, updateInfo)

	return nil
}

// UpdatePlayerLogin 更新玩家登录信息
func UpdatePlayerLogin(ctx context.Context, productId int32, playerId uint64, dto *model.UpdateLoginDto) error {
	if dto == nil {
		return nil
	}

	// 更新登录时间
	playerInfo := make(map[string]interface{})
	playerInfo["last_login_time"] = time.Now()
	playerInfo["last_login_device_code"] = dto.DeviceCode
	UpdatePlayerInfoDynamic(ctx, productId, playerId, config.PlayerInfoKey(productId, playerId), model.TablePlayerInfo, playerInfo)

	// 更新客户端ip等设备信息
	deviceInfo := make(map[string]interface{})
	deviceInfo["client_ip"] = dto.ClientIp
	UpdatePlayerInfoDynamic(ctx, productId, playerId, config.PlayerDevicesKey(productId, playerId), model.TablePlayerDevices, deviceInfo)

	return nil
}

// UpdatePlayerExtendInfo 更新玩家扩展信息
func UpdatePlayerExtendInfo(ctx context.Context, productId int32, playerId uint64, dto *model.UpdateExtendDto) error {
	if dto == nil {
		return nil
	}

	updateInfo := dto.GenerateUpdateInfo()

	return UpdatePlayerInfoDynamic(ctx, productId, playerId, config.PlayerExtendKey(productId, playerId), model.TablePlayerExtend, updateInfo)
}