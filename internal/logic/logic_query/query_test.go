package logicQuery

import (
	"context"
	"sync"
	"testing"
	"usersrv/internal/test"

	"github.com/sirupsen/logrus"
)

func TestQueryPlayerInfoById(t *testing.T) {
	test.InitSql()
	test.InitRedis()
	wg := &sync.WaitGroup{}
	for i:= 0; i < 100 ; i ++ {
		go func() {
			wg.Add(1)
			defer wg.Done()
			ctx := context.Background()
			data1, data2, err := QueryPlayerInfoById(ctx, 1, 101)
			if err != nil {
				t.Fatalf("QueryPlayerInfoById failed, err:%v", err)
			}
			logrus.Infof("QueryPlayerInfoById, data1:%+v ", data1)
			logrus.Infof("QueryPlayerInfoById, data2:%+v", data2)
		}()
	}
	wg.Wait()
}

func TestQuery(t *testing.T) {
	test.InitRedis()
	test.InitSql()

	master, info, err := QueryPlayerInfoById(context.TODO(), 1, 19)
	if err != nil {
		t.Fatalf("QueryPlayerInfoById failed, err:%v", err)
	}
	logrus.Infof("QueryPlayerInfoById, data1:%+v ", master)
	logrus.Infof("QueryPlayerInfoById, data2:%+v ", info)	
}