package logicDelete

import (
	"context"
	"time"
	daoDelete "usersrv/internal/dao/dao_delete"
	logicQuery "usersrv/internal/logic/logic_query"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

func DeleteAccount(ctx context.Context, productId int32, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)

	// 查询出玩家信息 删除redis
	pMaster, _, err := logicQuery.QueryPlayerInfoById(ctx, productId, playerId)
	if err != nil || pMaster == nil {
		entry.Warnf("delete player:%d, query player info error:%s", playerId, err.Error())
		return err
	}

	// 先删除redis中的玩家信息
	daoDelete.DeleteAccountRds(ctx, pMaster)

	// 延时双删
	time.AfterFunc(time.Second*3, func() {
		newCtx := context.Background()
		daoDelete.DeleteAccountRds(newCtx, pMaster)
	})

	// 更新到数据库
	err = daoDelete.DeletePlayerRdb(ctx, productId, playerId)
	if err != nil {
		return err
	}

	entry.Infof("delete productId:%d player:%d success", productId, playerId)

	return nil
}
