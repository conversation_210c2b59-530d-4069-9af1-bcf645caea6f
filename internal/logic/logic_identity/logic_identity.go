package logic_identity

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"time"
	"usersrv/internal/config"
	"usersrv/internal/dao/dao_identity"
	daoUpdate "usersrv/internal/dao/dao_update"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"git.keepfancy.xyz/back-end/frameworks/lib/utility"
	"github.com/ldy105cn/xorm"
)

// UpdateRealName 更新实名认证
func UpdateRealName(ctx context.Context, info *model.TPlayerRealNameAuth) error {
	// PS:使用 player Update相同的代码

	engine := mysql.GetMysqlEngine(dict_mysql.MysqlDBPlayer)
	playerId := info.PlayerId
	productId := info.ProductId
	entry := logx.NewLogEntry(ctx)

	_, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {

		var row int64
		var err error

		err = dao_identity.Modify(ctx, session, info)
		if err != nil {
			return 0, err
		}
		updateInfo := make(map[string]interface{})
		updateInfo["real_name_auth"] = true

		// 玩家详细信息
		// 先删除redis中的玩家信息
		daoUpdate.DelPlayerRds(ctx, playerId, config.PlayerMasterKey(productId, playerId))

		// 延时双删
		time.AfterFunc(time.Second*3, func() {
			ctx := context.Background()
			daoUpdate.DelPlayerRds(ctx, playerId, config.PlayerMasterKey(productId, playerId))
		})

		// 更新到数据库
		row, err = daoUpdate.UpdateInfoByFieldRdb(ctx, session, productId, playerId, model.TablePlayerMaster, updateInfo)
		if err != nil {
			return row, err
		}

		return row, err
	})

	if err != nil {
		entry.Errorf("update player:%d info error:%v", playerId, err)
		return err
	}

	entry.Infof("update player:%d info success", playerId)

	return nil
}

// RealNameStatus 查询实名认证状态
func RealNameStatus(ctx context.Context, productId int32, playerId uint64) (bool, error) {
	_, err := dao_identity.Get(ctx, productId, playerId)
	if errors.Is(err, redisx.Empty) || errors.Is(err, model.PlayerNil) {
		return false, nil
	}

	if err != nil {
		return false, err
	}

	return true, nil
}

// 查询用户年龄状态
func PlayerAgeQuery(ctx context.Context, productId int32, playerId uint64) (commonPB.USER_AGE, error) {
	info, err := dao_identity.Get(ctx, productId, playerId)
	if errors.Is(err, redisx.Empty) {
		return 0, fmt.Errorf("please real-name auth first")
	}
	if err != nil {
		return 0, err
	}

	age := utility.CalcAge(info.Year, info.Month, info.Year)
	if age <= 0 {
		return 0, protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	// 保证有序性
	list := make([]int, len(commonPB.USER_AGE_value))
	i := 0
	for _, val := range commonPB.USER_AGE_value {
		list[i] = int(val)
		i++
	}
	sort.Ints(list)

	for _, val := range list {
		if val > int(age) {
			return commonPB.USER_AGE(val), nil
		}
	}

	return commonPB.USER_AGE_UA_ADULT, nil
}
