package logicCreate

import (
	"context"
	"fmt"
	"math/rand"
	"time"
	"usersrv/internal/config"
	"usersrv/internal/dao/dao_master"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
)

// 序列与掩码位定义
// 注意：为兼容 MySQL BIGINT(有符号) 范围，保证总位宽 <= 63 位
const (
	sequenceBits uint = 55                              // 序列号位数（避免符号位）
	maskBits     uint = 8                               // 随机掩码位数
	maxSequence       = (uint64(1) << sequenceBits) - 1 // 最大序列号
	maxMask           = (uint64(1) << maskBits) - 1     // 最大掩码值
)

// IDGenerator ID生成器
type IDGenerator struct {
	keyTTL time.Duration
}

// NewIDGenerator 创建生成器
func NewIDGenerator(ttl time.Duration) *IDGenerator {
	return &IDGenerator{keyTTL: ttl}
}

// getLastSequenceFromDB 当Redis无值时从数据库恢复最大序列
func (g *IDGenerator) getLastSequenceFromDB(ctx context.Context, productId int32) (int64, error) {
	// 获取最大player_id
	maxID, err := dao_master.GetMaxPlayerID(ctx, productId)
	if err != nil {
		return 0, fmt.Errorf("get last max player_id failed: %w", err)
	}
	var seq int64
	if maxID > 0 {
		seq = int64(maxID >> maskBits)
	}
	return seq, nil
}

// NextID 生成下一个唯一ID
func (g *IDGenerator) NextID(ctx context.Context, productId int32) (uint64, error) {
	cli := redisx.GetPlayerCli()
	key := config.IDGenSequenceKey(productId)

	// 检查key，不存在则从DB恢复
	exists, err := cli.Exists(ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("redis EXISTS failed: %w", err)
	}
	if exists == 0 {
		lastSequence, err := g.getLastSequenceFromDB(ctx, productId)
		if err != nil {
			return 0, fmt.Errorf("get last sequence from DB failed: %w", err)
		}
		// 首次初始化，SETNX失败代表有并发已初始化，直接继续INCR即可
		if _, err := cli.SetNX(ctx, key, lastSequence, g.keyTTL).Result(); err != nil {
			return 0, fmt.Errorf("redis SETNX failed: %w", err)
		}
	}

	// INCR + EXPIRE 保持活跃
	pipe := cli.TxPipeline()
	seqCmd := pipe.Incr(ctx, key)
	pipe.Expire(ctx, key, g.keyTTL)
	if _, err := pipe.Exec(ctx); err != nil {
		return 0, fmt.Errorf("redis INCR/EXPIRE failed: %w", err)
	}
	sequenceI64, err := seqCmd.Result()
	if err != nil {
		return 0, fmt.Errorf("get INCR result failed: %w", err)
	}
	sequence := uint64(sequenceI64)
	if sequence > maxSequence {
		return 0, fmt.Errorf("sequence exhausted")
	}

	// 低8位随机掩码
	rand.Seed(time.Now().UnixNano())
	mask := uint64(rand.Int63n(int64(maxMask + 1)))

	// 高位为序列，低位为掩码
	id := (sequence << maskBits) | mask
	return id, nil
}

// GeneratePlayerID 对外暴露的便捷函数
func GeneratePlayerID(ctx context.Context, productId int32) (uint64, error) {
	gen := NewIDGenerator(config.IDGEN_EXPIRE)
	return gen.NextID(ctx, productId)
}
