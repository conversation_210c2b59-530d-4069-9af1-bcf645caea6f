package logicCreate

import (
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func init() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     "fancy_player",
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBPlayer: conf,
		dict_redis.RDBLock:   conf,
	})
}

func TestCreatePlayerRds(t *testing.T) {
	// logrus.SetLevel(logrus.DebugLevel)
	// playerId := uint64(1258)
	// pMasterInfo := &model.PlayerMaster{
	// 	PlayerID: playerId,
	// 	DeviceCode:  "123456a",
	// 	ProductID:   1,
	// }
	// playerInfo := &model.PlayerInfo{
	// 	PlayerID: playerId,
	// }
	// pDeviceInfo := &model.PlayerDevices{
	// 	PlayerID:    playerId,
	// 	ProductID:   1,
	// 	BundleName:  "BundleName",
	// 	DeviceName:  "DeviceName",
	// 	DeviceBrand: "DeviceBrand",
	// 	MacAddr:     "MacAddr",
	// }
	// playerId, err := CreatePlayerRds(context.Background(), playerId, pMasterInfo, playerInfo, pDeviceInfo)
	// t.Logf("playerId: %d, err: %v", playerId, err)
}

// TestGeneratePlayerID 测试玩家ID生成功能
func TestGeneratePlayerID(t *testing.T) {
	tests := []struct {
		name      string
		productId int32
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "正常生成玩家ID - 产品ID为1",
			productId: 1,
			wantErr:   false,
		},
		{
			name:      "正常生成玩家ID - 产品ID为100",
			productId: 100,
			wantErr:   false,
		},
		{
			name:      "正常生成玩家ID - 产品ID为999",
			productId: 999,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			// 调用被测试函数
			playerID, err := logicCommon.GeneratePlayerID(ctx, tt.productId)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
				return
			}

			// 验证没有错误
			require.NoError(t, err)

			// 验证生成的ID不为0
			assert.NotZero(t, playerID, "生成的玩家ID不应该为0")

			// 验证ID在合理范围内
			assert.Greater(t, playerID, uint64(0), "生成的ID应该大于0")

			t.Logf("产品ID: %d, 生成的玩家ID: %d", tt.productId, playerID)
		})
	}
}

// TestGeneratePlayerID_Concurrent 并发测试
func TestGeneratePlayerID_Concurrent(t *testing.T) {
	// 并发测试，确保生成的ID唯一
	productId := int32(1)
	ctx := context.Background()

	const goroutineCount = 10
	const idsPerGoroutine = 5

	var wg sync.WaitGroup
	var mu sync.Mutex
	var allIds []uint64
	var errors []error

	for i := 0; i < goroutineCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for j := 0; j < idsPerGoroutine; j++ {
				id, err := logicCommon.GeneratePlayerID(ctx, productId)

				mu.Lock()
				if err != nil {
					errors = append(errors, err)
				} else {
					allIds = append(allIds, id)
				}
				mu.Unlock()
			}
		}()
	}

	wg.Wait()

	// 验证没有错误
	assert.Empty(t, errors, "并发生成ID时不应该有错误")

	// 验证生成了预期数量的ID
	expectedCount := goroutineCount * idsPerGoroutine
	assert.Len(t, allIds, expectedCount, "应该生成预期数量的ID")

	// 验证所有ID都是唯一的
	idSet := make(map[uint64]bool)
	duplicates := 0
	for _, id := range allIds {
		if idSet[id] {
			duplicates++
		}
		idSet[id] = true
		assert.NotZero(t, id, "生成的ID不应该为0")
	}

	assert.Equal(t, 0, duplicates, "不应该有重复的ID")
	t.Logf("并发生成了 %d 个唯一ID", len(allIds))
}

// TestGeneratePlayerID_Sequential 连续生成测试
func TestGeneratePlayerID_Sequential(t *testing.T) {
	// 测试连续生成的ID是否都是唯一的
	productId := int32(1)
	ctx := context.Background()

	var ids []uint64
	for i := 0; i < 10; i++ {
		id, err := logicCommon.GeneratePlayerID(ctx, productId)
		require.NoError(t, err)
		require.NotZero(t, id)
		ids = append(ids, id)

		t.Logf("第 %d 个ID: %d", i+1, id)
	}

	// 验证所有ID都不相同
	idSet := make(map[uint64]bool)
	for _, id := range ids {
		assert.False(t, idSet[id], "ID %d 重复了", id)
		idSet[id] = true
	}
}

// TestGeneratePlayerID_ContextCancellation 上下文取消测试
func TestGeneratePlayerID_ContextCancellation(t *testing.T) {
	// 测试上下文取消的情况
	productId := int32(1)

	// 创建一个会被取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	_, err := logicCommon.GeneratePlayerID(ctx, productId)

	// 根据实际实现，可能会返回上下文取消错误或者正常执行
	// 这里我们主要确保函数能够处理取消的上下文而不会panic
	t.Logf("使用已取消的上下文调用结果: err=%v", err)
}

// BenchmarkGeneratePlayerID 性能基准测试
func BenchmarkGeneratePlayerID(b *testing.B) {
	productId := int32(1)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := logicCommon.GeneratePlayerID(ctx, productId)
		if err != nil {
			b.Fatal(err)
		}
	}
}
