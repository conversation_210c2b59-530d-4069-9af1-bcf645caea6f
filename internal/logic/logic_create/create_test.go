package logicCreate

import (
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func init() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     "fancy_player",
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBPlayer: conf,
		dict_redis.RDBLock:   conf,
	})
}

func TestCreatePlayerRds(t *testing.T) {
	// logrus.SetLevel(logrus.DebugLevel)
	// playerId := uint64(1258)
	// pMasterInfo := &model.PlayerMaster{
	// 	PlayerID: playerId,
	// 	DeviceCode:  "123456a",
	// 	ProductID:   1,
	// }
	// playerInfo := &model.PlayerInfo{
	// 	PlayerID: playerId,
	// }
	// pDeviceInfo := &model.PlayerDevices{
	// 	PlayerID:    playerId,
	// 	ProductID:   1,
	// 	BundleName:  "BundleName",
	// 	DeviceName:  "DeviceName",
	// 	DeviceBrand: "DeviceBrand",
	// 	MacAddr:     "MacAddr",
	// }
	// playerId, err := CreatePlayerRds(context.Background(), playerId, pMasterInfo, playerInfo, pDeviceInfo)
	// t.Logf("playerId: %d, err: %v", playerId, err)
}
