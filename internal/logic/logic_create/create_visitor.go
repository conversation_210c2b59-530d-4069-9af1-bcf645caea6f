package logicCreate

import (
	"context"
	"errors"
	"usersrv/internal/config"
	daoQuery "usersrv/internal/dao/dao_query"
	logicQuery "usersrv/internal/logic/logic_query"
	"usersrv/internal/model"

	daoVisitor "usersrv/internal/dao/dao_create/dao_visitor"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"
)

type CreateVisitor struct {
}

func (c *CreateVisitor) CreatePlayer(ctx context.Context, req *userRpc.CreatePlayerReq) (*userRpc.CreatePlayerRsp, error) {
	// 判断平台和设置id是否有效
	productId := req.GetProductId()
	entry := logx.NewLogEntry(ctx)

	// 平台id检查
	if productId <= 0 {
		entry.Errorf("create visitor player fail req:%s", req.String())
		return nil, errors.New("product is not valid")
	}

	// 设备信息检查
	deviceCode := req.GetDeviceInfo().GetDeviceCode()
	if deviceCode == "" {
		entry.Errorf("create visitor player fail req:%s", req.String())
		return nil, errors.New("device is not valid")
	}

	// 判断玩家是否存在
	// 判断当前玩家是否存在
	playerId, err := QueryPlayerIdByDeviceCode(ctx, productId, deviceCode, req.GetCreateType())
	if err != nil {
		return nil, err
	}
	// player == 0 表示玩家不存在
	if err == nil && playerId > 0 {
		entry.Warnf("create player, productId:%d, deviceCode:%s, already exist, playerId:%d", productId, deviceCode, playerId)

		// 查询玩家信息 并返回结果
		pMasterInfo, playerInfo, err := logicQuery.QueryPlayerInfoById(ctx, productId, playerId)
		if err != nil {
			entry.Errorf("get playerId:%d info err:%v", playerId, err)
			return nil, err
		}

		pUserInfo, err := model.MakeUpRichUserInfo(pMasterInfo, playerInfo, nil)
		if err != nil {
			entry.Errorf("makeUp Rich playerId:%d info err:%v", playerId, err)
			return nil, err
		}

		rsp := &userRpc.CreatePlayerRsp{
			PlayerId:     playerId,
			RichUserInfo: pUserInfo,
		}

		entry.Infof("create player, productId:%d, deviceCode:%s already exist, playerId:%d, rsp info:%s", productId, deviceCode, playerId, rsp.String())

		return rsp, nil
	}

	pMaster, pInfo, pDeviceInfo := CreateProtoToModel(req)
	playerId, err = CreatePlayer(ctx, pMaster, pInfo, pDeviceInfo)
	if err != nil {
		entry.Errorf("create player param:%s, err:%v", req.String(), err)
		return nil, err
	}

	pUserInfo, err := model.MakeUpRichUserInfo(pMaster, pInfo, nil)
	if err != nil {
		entry.Errorf("create player param:%s, playerId:%d, err:%v", req.String(), playerId, err)
		return nil, err
	}

	rsp := &userRpc.CreatePlayerRsp{
		PlayerId:     playerId,
		RichUserInfo: pUserInfo,
	}

	entry.Infof("create visitor player param:%s", req.String())

	return rsp, nil
}

// QueryPlayerIdByDeviceCode 查询用户id
func QueryPlayerIdByDeviceCode(ctx context.Context, productId int32, deviceCode string, loginType commonPB.LOGIN_TYPE) (uint64, error) {
	entry := logx.NewLogEntry(ctx)

	var err error
	var playerId uint64

	// 这里使用singleflight 查询
	sfkey := config.GetSfgPlayerByDeviceKey(productId, deviceCode, loginType)
	val, err := singleflight.DoCtx(ctx, sfkey, func() (interface{}, error) {
		playerId, err = daoVisitor.QueryPlayerIdByDeviceRds(ctx, productId, deviceCode, loginType)
		if err != nil {
			return model.InvalidId, err
		}

		// 默认0值为查询不到
		if playerId == 0 {
			// 查询数据库
			fieldMap := map[string]interface{}{
				"product_id":  productId,
				"device_code": deviceCode,
				"login_type":  loginType,
			}

			playerId, err = daoQuery.QueryPlayerIdForFiledRdb(ctx, model.TablePlayerMaster, fieldMap)
			if err != nil && !errors.Is(err, model.PlayerNil) {
				return model.InvalidId, err
			}
		}
		// 重新设置缓存
		{
			daoVisitor.CachePlayerIdByDeviceRds(ctx, productId, playerId, deviceCode, loginType)
		}
		return playerId, nil
	})

	if err != nil {
		entry.Errorf("query player id for product:%d deviceCode:%s, singleflight error: %+v", productId, deviceCode, err)
		return 0, err
	}

	entry.Infof("query player id for product:%d deviceCode:%s, db ok, playerId:%d", productId, deviceCode, playerId)

	return val.(uint64), nil
}
