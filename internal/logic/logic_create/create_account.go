package logicCreate

import (
	"context"
	"errors"

	"usersrv/internal/config"
	"usersrv/internal/model"

	daoAccount "usersrv/internal/dao/dao_create/dao_account"
	daoQuery "usersrv/internal/dao/dao_query"

	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"
)

type CreateAccount struct {
}

func (c *CreateAccount) CreatePlayer(ctx context.Context, req *userRpc.CreatePlayerReq) (*userRpc.CreatePlayerRsp, error) {
	// 判断账号和密码是否存在
	accountInfo := req.GetAccountInfo()
	entry := logx.NewLogEntry(ctx)

	rsp := &userRpc.CreatePlayerRsp{}
	if accountInfo == nil || accountInfo.GetAccount() == "" || accountInfo.GetPassword() == "" {
		return rsp, errors.New("account info is nil")
	}

	// 判断账号是否存在
	productId := req.GetProductId()
	playerId, err := QueryPlayerIdByAccount(ctx, productId, accountInfo.GetAccount(), accountInfo.GetPassword())
	if playerId > 0 || err != nil {
		return rsp, errors.New("player is exist")
	}

	// 创建账号
	pMaster, pInfo, pDeviceInfo := CreateProtoToModel(req)
	playerId, err = CreatePlayer(ctx, pMaster, pInfo, pDeviceInfo)
	if err != nil {
		entry.Errorf("create player param:%s, err:%v", req.String(), err)
		return rsp, err
	}

	pUserInfo, err := model.MakeUpRichUserInfo(pMaster, pInfo, nil)
	if err != nil {
		entry.Errorf("create player param:%s, playerId:%d, err:%+v", req.String(), playerId, err)
		return rsp, err
	}

	rsp = &userRpc.CreatePlayerRsp{
		PlayerId:     playerId,
		RichUserInfo: pUserInfo,
	}

	entry.Infof("create account player param:%s, playerId:%d", req.String(), playerId)

	return rsp, nil
}


// QueryPlayerIdByAccount 根据账号查询玩家id
func QueryPlayerIdByAccount(ctx context.Context, productId int32, account string, password string) (uint64, error) {
	// 先查询redis 只查账号
	entry := logx.NewLogEntry(ctx)

	// 使用 singleflight 查询
	sfkey := config.GetSfgPlayerByAccountKey(productId, account)
	val, err := singleflight.DoCtx(ctx, sfkey, func() (interface{}, error) {
		accountPlayer := daoAccount.QueryPlayerIdByAccountRds(ctx, productId, account, password)
		if accountPlayer != nil {
			// 如果Redis中存在玩家信息，但密码不匹配
			if password != "" && accountPlayer.Password != password {
				entry.Infof("query player id for product:%d account:%s, redis ok, playerId:%d", productId, account, accountPlayer.PlayerId)
				return model.InvalidId, errors.New("password error")
			}

			entry.Infof("query player id for product:%d account:%s, redis ok, playerId:%d", productId, account, accountPlayer.PlayerId)
			return accountPlayer.PlayerId, nil
		}

		// 构建查询字段映射
		fieldMap := map[string]interface{}{
			"product_id": productId,
			"account":    account,
		}

		// 如果密码不为空，则添加密码字段到查询映射中
		if password != "" {
			fieldMap["password"] = password
		}

		// 从数据库中查询玩家ID
		playerId, err := daoQuery.QueryPlayerIdForFiledRdb(ctx, model.TablePlayerMaster, fieldMap)

		// 如果查询结果为空，但无错误，则记录警告日志并返回空结果
		if errors.Is(err, model.PlayerNil) {
			entry.Warnf("query player id for product:%d account:%s, password:%s, db error: %v", productId, account, password, err)
			return model.InvalidId, nil
		}

		// 如果查询出现错误
		if err != nil {
			entry.Warnf("query player id for product:%d account:%s,  password:%s, db error: %v", productId, account, password, err)
			return model.InvalidId, err
		}

		// 第一次不带passwd，第二次请求带password
		if password != "" {
			daoAccount.CachePlayerIdByAccountRds(ctx, productId, playerId, account, password)
		}

		return playerId, nil
	})

	if err != nil {
		entry.Errorf("query player id for product:%d account:%s,  password:%s, db error: %v", productId, account, password, err)
		return 0, err
	}

	playerId := val.(uint64)

	entry.Infof("query player id for product:%d account:%s,  password:%s, db ok, playerId:%d", productId, account, password, playerId)

	return playerId, nil
}
