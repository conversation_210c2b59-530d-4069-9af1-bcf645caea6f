package logicCreate

import (
	"context"
	"errors"
	"usersrv/internal/config"
	logicCommon "usersrv/internal/logic/logic_common"
	"usersrv/internal/model"

	daoThird "usersrv/internal/dao/dao_create/dao_third"
	daoQuery "usersrv/internal/dao/dao_query"

	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/singleflight"
)

type CreateThird struct {
}

func (c *CreateThird) CreatePlayer(ctx context.Context, req *userRpc.CreatePlayerReq) (*userRpc.CreatePlayerRsp, error) {
	// 判断平台和设置id是否有效
	productId := req.GetProductId()
	createType := req.GetCreateType()
	entry := logx.NewLogEntry(ctx)

	thirdInfo := req.GetThirdInfo()
	rsp := &userRpc.CreatePlayerRsp{}

	// 判断账号是否存在
	playerId, err := QueryPlayerIdByOpenId(ctx, productId, int32(logicCommon.ConvertLoginTypeToAccType(createType)), thirdInfo.GetOpenId())
	if playerId > 0 || err != nil {
		return rsp, errors.New("player is exist")
	}

	// 创建账号
	pMaster, pInfo, pDeviceInfo := CreateProtoToModel(req)
	playerId, err = CreatePlayer(ctx, pMaster, pInfo, pDeviceInfo)
	if err != nil {
		entry.Errorf("create player param:%s, err:%v", req.String(), err)
		return rsp, err
	}

	pUserInfo, err := model.MakeUpRichUserInfo(pMaster, pInfo, nil)
	if err != nil {
		entry.Errorf("create player param:%s, playerId:%d, err:%v", req.String(), playerId, err)
		return rsp, errors.New("MakeUpRichUserInfo error")
	}

	rsp = &userRpc.CreatePlayerRsp{
		PlayerId:     playerId, 
		RichUserInfo: pUserInfo,
	}

	entry.Infof("create third player param:%s, playerId:%d", req.String(), playerId)

	return rsp, nil
}


// QueryPlayerIdByOpenId 根据openId查询玩家id 这里是通用接口 使用账号类型查询
func QueryPlayerIdByOpenId(ctx context.Context, productId int32, accType int32, openId string) (uint64, error) {
	entry := logx.NewLogEntry(ctx)

	// 这里使用singleflight 查询
	var err error
	var playerId uint64

	sfkey := config.GetSfgPlayerByOpenIdKey(productId, accType, openId)
	val, err := singleflight.DoCtx(ctx, sfkey, func() (interface{}, error) {
		playerId, err = daoThird.QueryPlayerIdByOpenIdRds(ctx, productId, accType, openId)
		if err != nil {
			return model.InvalidId, err
		}
		
		// 默认0值为查询不到
		if playerId == 0 {
			// 查询数据库
			fieldMap := map[string]interface{}{
				"product_id": productId,
				"acc_type":   accType,
				"open_id": openId,
			}

			playerId, err = daoQuery.QueryPlayerIdForFiledRdb(ctx, model.TablePlayerMaster, fieldMap)
			if err != nil && !errors.Is(err, model.PlayerNil) {
				return model.InvalidId, err
			}
		}
		// 重新设置缓存
		{
			daoThird.CachePlayerIdByOpenIdRds(ctx, productId, playerId, accType, openId)
		}
		return playerId, nil
	})

	if err != nil {
		entry.Errorf("query player id for product:%d accType:%d, openId:%s, singleflight error: %+v", productId, accType, openId, err)
		return 0, err
	}

	entry.Infof("query player id for product:%d accType:%d, openId:%s, db ok, playerId:%d", productId, accType, openId, playerId)

	return val.(uint64), nil
}