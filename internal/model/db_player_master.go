// Package model File: db_player_master.go 玩家表
package model

import (
	"fmt"
	"time"

	logicCommon "usersrv/internal/logic/logic_common"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"git.keepfancy.xyz/back-end/frameworks/lib/utility"
	"github.com/sirupsen/logrus"
)

const TablePlayerMaster = "t_player_master"

// 账号信息-登录
// PlayerMaster 玩家基础表
type PlayerMaster struct {
	// 基础信息
	PlayerID    uint64                 `xorm:"pk autoincr comment('自增') BIGINT(20) player_id" json:"player_id"`
	ProductID   int32                  `xorm:"product_id" json:"product_id"`     // 产品 id
	ChannelID   commonPB.CHANNEL_TYPE  `xorm:"channel_id" json:"channel_id"`     // 渠道 id
	Platform    commonPB.PLATFORM_TYPE `xorm:"platform" json:"platform"`         // 平台 id
	Status      commonPB.ACC_STATUS    `xorm:"status" json:"status"`             // 账号状态
	MigrateFrom string                 `xorm:"migrate_from" json:"migrate_from"` // 迁移账号来源

	// 账号密码
	Account  string `xorm:"account" json:"account"`   // 账号
	Password string `xorm:"password" json:"password"` // 密码

	// 登录类型 - 账号信息
	AccType          commonPB.ACC_TYPE   `xorm:"acc_type" json:"acc_type"`                     // 账号登录类型
	LastLoginType    commonPB.LOGIN_TYPE `xorm:"last_login_type" json:"last_login_type"`       // 上次账号登录类型
	LoginType        commonPB.LOGIN_TYPE `xorm:"login_type" json:"login_type"`                 // 账号登录类型 - 游客，FB，苹果
	AdjustID         string              `xorm:"adjust_id" json:"adjust_id"`                   // 设备 adjust_id(ADID)
	WechatAppID      string              `xorm:"wechat_app_id" json:"wechat_app_id"`           // 微信 appid
	WechatOpenID     string              `xorm:"wechat_open_id" json:"wechat_open_id"`         // 微信 openid
	WechatUnionID    string              `xorm:"wechat_union_id" json:"wechat_union_id"`       // 微信 unionId
	WechatSessionKey string              `xorm:"wechat_session_key" json:"wechat_session_key"` // 微信会话 key
	DeviceCode       string              `xorm:"device_code" json:"device_code"`               // 设备码
	OpenID           string              `xorm:"open_id" json:"open_id"`                       // openid
	AppleID          string              `xorm:"apple_id" json:"apple_id"`                     // appleID
	AppleToken       string              `xorm:"apple_token" json:"apple_token"`               // AppleToken
	Email            string              `xorm:"email" json:"email"`                           // email
	Phone            string              `xorm:"phone" json:"phone"`                           // 手机号
	RealNameAuth     bool                `xorm:"real_name_auth" json:"real_name_auth"`         // 是否已经实名认证

	// 客户端信息
	ClientVersion string                 `xorm:"client_version" json:"client_version"` // 客户端版本号
	AppLanguage   commonPB.LANGUAGE_TYPE `xorm:"app_language" json:"app_language"`     // app语言

	// 封号信息
	BanReason commonPB.BAN_ACC_REASON_TYPE `xorm:"ban_reason" json:"ban_reason"` // 封号原因
	BanTime   time.Time                    `xorm:"ban_time" json:"ban_time"`     // 封号结束时间

	DeletedAt  time.Time `xorm:"default null deleted" json:"deleted_time"` // 删除时间(账号注销标识)
	CreateTime time.Time `xorm:"'create_time' created" json:"create_time"` // 创建时间
	UpdateTime time.Time `xorm:"'update_time' updated" json:"update_time"` // 更新时间
}

func (data PlayerMaster) TableName() string {
	return TablePlayerMaster
}

func (data PlayerMaster) String() string {
	return fmt.Sprintf("%#v", data)
}

// NewPlayerMasterFromRdsHash 初始化PlayerMaster 从redis中获取数据
func NewPlayerMasterFromRdsHash(data map[string]string) *PlayerMaster {
	if len(data) == 0 {
		return nil
	}
	obj := &PlayerMaster{}
	transform.Map2Struct(data, obj)
	return obj
}

// ToRedisHash 转换为redis的hash map
func (data PlayerMaster) ToRedisHash() map[string]interface{} {
	hash := make(map[string]any)
	err := transform.Struct2Map(data, hash)
	if err != nil {
		logrus.Warnf("hash convert to redis hash failed:%+v", err)
		return nil
	}
	return hash
}

// IsValid 判断是否有效 有一个字段有效就返回true
func (data PlayerMaster) IsValid() bool {
	mapData := data.ToRedisHash()

	for k, v := range mapData {

		switch k {
		case "channel_id":
			v = commonPB.CHANNEL_TYPE_value[v.(string)]
		case "app_language":
			v = commonPB.LANGUAGE_TYPE_value[v.(string)]
		case "status":
			v = commonPB.ACC_STATUS_value[v.(string)]
		case "login_type":
			v = commonPB.LOGIN_TYPE_value[v.(string)]
		case "acc_type":
			v = commonPB.ACC_TYPE_value[v.(string)]
		}

		if utility.CheckValid(v) && k != "update_time" && k != "create_time" {
			return true
		}
	}
	return false
}

func NewPlayerMasterFromProto(proto *userRpc.CreatePlayerReq) *PlayerMaster {
	if proto == nil || proto.GetDeviceInfo() == nil {
		return nil
	}

	deviceInfo := proto.GetDeviceInfo()
	accountInfo := proto.GetAccountInfo()

	if accountInfo == nil {
		accountInfo = &commonPB.AccountInfo{}
	}

	accType := logicCommon.ConvertLoginTypeToAccType(proto.GetCreateType())
	var openId string 
	if proto.GetThirdInfo() != nil && logicCommon.IsThirdLoginType(accType) {
		openId = proto.GetThirdInfo().GetOpenId()
	}
	
	playerMaster := &PlayerMaster{
		ProductID: proto.GetProductId(),
		ChannelID: proto.GetChannelId(),
		Platform:  proto.GetPlatform(),

		Account:  accountInfo.GetAccount(),
		Password: accountInfo.GetPassword(),

		LoginType: proto.GetCreateType(),
		AccType:  accType,

		ClientVersion: proto.GetClientVersion(),
		DeviceCode:    deviceInfo.GetDeviceCode(),
		AdjustID:      deviceInfo.GetAdjustId(),
		AppLanguage:   deviceInfo.GetAppLanguage(),
		OpenID:        openId,
		CreateTime:    timex.Now(),
	}

	return playerMaster
}
