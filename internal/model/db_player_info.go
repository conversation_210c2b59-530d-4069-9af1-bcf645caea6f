// Package model File: db_player_info.go 玩家表
package model

import (
	"fmt"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"git.keepfancy.xyz/back-end/frameworks/lib/utility"
	"github.com/sirupsen/logrus"
)

const TablePlayerInfo = "t_player_info"

// 用户信息 - 用户游玩过程中修改
// PlayerInfo 玩家详细信息表
type PlayerInfo struct {
	PlayerID     uint64                `xorm:"player_id pk" json:"player_id"`
	ProductID    int32                 `xorm:"product_id" json:"product_id"`       // 产品 id
	ChannelID    commonPB.CHANNEL_TYPE `xorm:"channel_id" json:"channel_id"`       // 渠道 id
	ShowID       string                `xorm:"show_id" json:"show_id"`             // 展示id
	NickName     string                `xorm:"nick_name" json:"nick_name"`         // 昵称
	RealName     string                `xorm:"real_name" json:"real_name"`         // 真实姓名
	ThirdName    string                `xorm:"third_name" json:"third_name"`       // 第三方昵称名
	Country      string                `xorm:"country" json:"country"`             // 国家信息
	LanguageType string                `xorm:"language_type" json:"language_type"` // 语言区
	IdCard       string                `xorm:"id_card" json:"id_card"`
	Sex          int                   `xorm:"sex" json:"sex"`       // 性别
	Avatar       int64                 `xorm:"avatar" json:"avatar"` // 头像
	Frame        int64                 `xorm:"frame" json:"frame"`   // 头像框
	Lev          int32                 `xorm:"lev" json:"lev"`       // 角色等级
	Novice       bool                  `xorm:"novice" json:"novice"` // 是否新手
	UpdateTime   time.Time             `xorm:"update_time" json:"update_time"`
	DeletedAt    time.Time             `xorm:"default null deleted" json:"deleted_time"` // 删除时间(账号注销标识)

	// 登录信息
	LastLoginTime       time.Time `xorm:"last_login_time" json:"last_login_time"`               // 上次登录时间
	OfflineTime         time.Time `xorm:"offline_time" json:"offline_time"`                     // 离线时间
	LastLoginDeviceCode string    `xorm:"last_login_device_code" json:"last_login_device_code"` // 上次登录的设备号

}

func (data PlayerInfo) TableName() string {
	return TablePlayerInfo
}

func (data PlayerInfo) String() string {
	return fmt.Sprintf("%#v", data)
}

func NewPlayerInfoFromProto(proto *userRpc.CreatePlayerReq) *PlayerInfo {
	if proto == nil {
		return nil
	}

	pPlayer := &PlayerInfo{
		ProductID:  proto.GetProductId(),
		ChannelID:  proto.GetChannelId(),
		Frame:      int64(commonPB.USER_INIT_INFO_UIF_FRAME),
		Avatar:     int64(commonPB.USER_INIT_INFO_UIF_AVATAR),
		UpdateTime: timex.Now(),
		Country:    proto.GetRegion().GetCountry(),
	}

	return pPlayer
}

// NewPlayerInfoFromRdsHash 从redis hash 初始化数据
func NewPlayerInfoFromRdsHash(data map[string]string) *PlayerInfo {
	if len(data) == 0 {
		return nil
	}
	obj := &PlayerInfo{}
	err := transform.Map2Struct(data, obj)
	if err != nil {
		return nil
	}

	return obj
}

// ToRedisHash 转换为redis hash
func (data PlayerInfo) ToRedisHash() map[string]interface{} {
	hash := make(map[string]any)
	err := transform.Struct2Map(data, hash)
	if err != nil {
		logrus.Warnf("hash convert to redis hash failed:%+v", err)
		return nil
	}
	return hash
}

// IsValid 判断是否有效 有一个字段有效就返回true
func (data PlayerInfo) IsValid() bool {
	mapData := data.ToRedisHash()

	for k, v := range mapData {
		switch k {
		case "channel_id":
			v = commonPB.CHANNEL_TYPE_value[v.(string)]
		case "last_login_type":
			v = commonPB.LOGIN_TYPE_value[v.(string)]
		}

		if utility.CheckValid(v) && k != "update_time" {
			return true
		}
	}
	return false
}
