package model

import (
	"fmt"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

const TablePlayerExtend = "t_player_extend"

type PlayerExtend struct {
	PlayerID    uint64 `xorm:"player_id pk" json:"player_id"`                // 玩家id
	ProductID   int32  `xorm:"product_id" json:"product_id"`                 // 产品id
	NoviceGuide int32  `xorm:"novice_guide" json:"novice_guide"`             // 新手引导进度
	UpdateTime  time.Time `xorm:"update_time" json:"update_time"`            // 更新时间
	DeletedAt   time.Time `xorm:"default null deleted" json:"deleted_time"`  // 删除时间(账号注销标识)  
}

func (data PlayerExtend) TableName() string {
	return TablePlayerExtend
}

func (data PlayerExtend) String() string {
	return fmt.Sprintf("%#v", data)
}

func (data PlayerExtend) ToRedisHash() map[string]interface{} { 
	hash := make(map[string]interface{})
	err := transform.Struct2Map(data, hash)
	if err != nil {
		logrus.Warnf("hash convert to redis hash failed:%+v", err)
		return nil
	}

	return hash
}

func NewPlayerExtendFromRdsHash(data map[string]string) *PlayerExtend {
	if len(data) == 0 {
		return &PlayerExtend{}
	}
	obj := &PlayerExtend{}
	transform.Map2Struct(data, obj)
	return obj
}

func (data PlayerExtend) ToProto() *commonPB.ExtendUserInfo {
	return &commonPB.ExtendUserInfo{
		NoviceGuide: data.NoviceGuide,
	}
}
