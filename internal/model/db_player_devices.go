// Package model File: db_player_master.go 玩家表
package model

import (
	"fmt"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"git.keepfancy.xyz/back-end/frameworks/lib/utility"
	"github.com/sirupsen/logrus"
)

const TablePlayerDevices = "t_player_devices"

// PlayerDevices 玩家设备信息表
type PlayerDevices struct {
	PlayerID      uint64                `xorm:"player_id pk" json:"player_id"`
	ProductID     int32                 `xorm:"product_id" json:"product_id"`           // 产品 id
	BundleName    string                `xorm:"bundle_name" json:"bundle_name"`         // 客户端包名
	DeviceName    string                `xorm:"device_name" json:"device_name"`         // 设备名
	DeviceBrand   string                `xorm:"device_brand" json:"device_brand"`       // 手机品牌
	DeviceModel   string                `xorm:"device_model" json:"device_model"`       // 手机型号
	Mvno          string                `xorm:"mvno" json:"mvno"`                       // 运营商
	AndroidID     string                `xorm:"android_id" json:"android_id"`           // android_id
	OsLanguage    string                `xorm:"os_language" json:"os_language"`         // 操作系统语言
	MacAddr       string                `xorm:"mac_addr" json:"mac_addr"`               // mac地址
	ClientIp      string                `xorm:"client_ip" json:"client_ip"`             // ip
	Network_Type  commonPB.NETWORK_TYPE `xorm:"network_type" json:"network_type"`       // network
	Os            string                `xorm:"os" json:"os"`                           // os
	OsVersion     string                `xorm:"os_version" json:"os_version"`           // os_version
	FirebaseToken string                `xorm:"firebase_token" json:"firebase_token"`   // firebase_token
	Idfa          string                `xorm:"idfa" json:"idfa"`                       // idfa
	UpdateTime    time.Time             `xorm:"update_time updated" json:"update_time"` // update_time
	Resolution    string                `xorm:"resolution" json:"resolution"`           // 分辨率
	Cpu           string                `xorm:"cpu" json:"cpu"`                         // cpu
	Directx       string                `xorm:"directx" json:"directx"`                 // 显卡
	Ram           string                `xorm:"ram" json:"ram"`                         // 内存
	VideoAdapter  string                `xorm:"video_adapter" json:"video_adapter"`     //显卡
	DeletedAt     time.Time             `xorm:"default null deleted" json:"deleted_time"`// 删除时间(账号注销标识)
}

func (data *PlayerDevices) TableName() string {
	return TablePlayerDevices
}

func (data *PlayerDevices) String() string {
	return fmt.Sprintf("%#v", data)
}

func (data *PlayerDevices) ToProto() *commonPB.DeviceInfo {
	if data == nil {
		return nil
	}

	return &commonPB.DeviceInfo{
		DeviceModel: data.DeviceModel,
		DeviceBrand: data.DeviceBrand,
		DeviceName:  data.DeviceName,
		Os:          data.Os,
		OsLanguage:  data.OsLanguage,
		Idfa:        data.Idfa,
		Android:     data.AndroidID,
	}
}

// ToRedisHash 转换为redis hash 结构
func (data *PlayerDevices) ToRedisHash() map[string]interface{} {
	hash := make(map[string]any)
	err := transform.Struct2Map(data, hash)
	if err != nil {
		logrus.Warnf("hash convert to redis hash failed:%+v", err)
		return nil
	}

	return hash
}

// IsValid 判断是否有效 有一个字段有效就返回true
func (data PlayerDevices) IsValid() bool {
	mapData := data.ToRedisHash()

	for k, v := range mapData {
		if k == "network" {
			v = commonPB.NETWORK_TYPE_value[v.(string)]
		}

		if utility.CheckValid(v) && k != "update_time" {
			return true
		}
	}
	return false
}

// NewDeviceInfoFromRdsHash 初始化设备信息
func NewDeviceInfoFromRdsHash(data map[string]string) *PlayerDevices {
	if len(data) == 0 {
		return nil
	}

	deviceInfo := &PlayerDevices{}

	transform.Map2Struct(data, deviceInfo)

	return deviceInfo
}

func NewDeviceInfoFromProto(proto *userRpc.CreatePlayerReq) *PlayerDevices {
	if proto == nil || proto.GetDeviceInfo() == nil {
		return nil
	}

	deviceInfo := proto.GetDeviceInfo()
	
	// 设备信息
	playerDevice := &PlayerDevices{
		ProductID:    proto.GetProductId(),
		BundleName:   proto.GetBundleName(),
		Network_Type: proto.GetNetwork(),
		DeviceBrand:  deviceInfo.GetDeviceBrand(),
		DeviceModel:  deviceInfo.GetDeviceModel(),
		DeviceName:   deviceInfo.GetDeviceName(),
		Mvno:         deviceInfo.GetMvno(),
		OsLanguage:   deviceInfo.GetOsLanguage(),
		Idfa:         deviceInfo.GetIdfa(),
		Os:           deviceInfo.GetOs(),
		ClientIp:     deviceInfo.GetClientIp(),
		AndroidID:    deviceInfo.GetAndroid(),
		Resolution:   deviceInfo.GetResolution(),
		Cpu:          deviceInfo.GetCpu(),
		Directx:      deviceInfo.GetDirectx(),
		Ram:          deviceInfo.GetRam(),
		VideoAdapter: deviceInfo.GetVideoAdapter(),
		UpdateTime:   timex.Now(),
	}

	return playerDevice
}