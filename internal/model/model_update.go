package model

import (
	"errors"
	"reflect"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/reflectext"
	"github.com/sirupsen/logrus"
)

// TableStructMap 表名和结构体类型的映射
var TableStructMap = map[string]reflect.Type{
	TablePlayerInfo:    reflect.TypeOf(&PlayerInfo{}),
	TablePlayerMaster:  reflect.TypeOf(&PlayerMaster{}),
	TablePlayerDevices: reflect.TypeOf(&PlayerDevices{}),
	TablePlayerExtend:  reflect.TypeOf(&PlayerExtend{}),
}

// MakeUpRichUserInfo 根据玩家基础信息和详细信息组合成玩家信息
func MakeUpRichUserInfo(pMaster *PlayerMaster, pUserInfo *PlayerInfo, pExtend *PlayerExtend) (*commonPB.RichUserInfo, error) {

	if pMaster == nil || pUserInfo == nil {
		logrus.Warnf("not found pMaster:%+v or pUserInfo:%+v", pMaster, pUserInfo)
		return &commonPB.RichUserInfo{}, errors.New("player not found")
	}

	briefInfo, err := MakeUpBriefUserInfo(pUserInfo)
	if err != nil {
		logrus.Errorf("MakeUpRichUserInfo: MakeUpBriefUserInfo error: %v", err)
		return &commonPB.RichUserInfo{}, err
	}

	richInfo := &commonPB.RichUserInfo{
		BriefUserInfo: briefInfo,

		// TODO 建议详细信息和基础信息使用两个结构 同数据库
		AppVersion:   pMaster.ClientVersion,
		Platform:     pMaster.Platform,
		RegisterTime: pMaster.CreateTime.Unix(),
		AppLanguage:  pMaster.AppLanguage,
		AccType:      pMaster.AccType,
		RealNameAuth: pMaster.RealNameAuth,
	}

	if pExtend != nil {
		richInfo.ExtendUserInfo = pExtend.ToProto()
	}

	// XXX: 状态判断需要优化
	// 封号信息
	if pMaster.Status != commonPB.ACC_STATUS_AS_NORMAL {
		richInfo.BanAccountInfo = &commonPB.BanAccountInfo{
			BanReason:    pMaster.BanReason,
			BanLoginTime: pMaster.BanTime.Unix(),
		}
		logrus.Debugf("playerId:%d, banReason:%s", pMaster.PlayerID, pMaster.BanReason)
	}

	return richInfo, nil
}

// MakeUpBriefUserInfo 组合玩家简要信息
func MakeUpBriefUserInfo(pUserInfo *PlayerInfo) (*commonPB.BriefUserInfo, error) {

	if pUserInfo == nil {
		logrus.Errorf("MakeUpBriefUserInfo: pUserInfo is nil")
		return nil, errors.New("param error")
	}

	// 组装玩家简要信息
	briefInfo := &commonPB.BriefUserInfo{
		PlayerId: pUserInfo.PlayerID,
		Name:     pUserInfo.NickName,
		Avatar:   pUserInfo.Avatar,
		Frame:    pUserInfo.Frame,
		Lev:      pUserInfo.Lev,
		Country:  pUserInfo.Country,
		LastDeviceCode: pUserInfo.LastLoginDeviceCode,

		// LastLogoutTime: pUserInfo.OfflineTime.Unix(),
		// LastLoginTime:  pUserInfo.LastLoginTime.Unix(),
		// LastDeviceCode: pUserInfo.LastLoginDeviceCode,
	}

	return briefInfo, nil
}

type UpdateLoginDto struct {
	*commonPB.DeviceInfo
}
 
type UpdateExtendDto struct {
	*commonPB.ExtendUserInfo
}

// 根据UserExtendInfo中的数据生成UpdateInfo
func (dto *UpdateExtendDto) GenerateUpdateInfo() map[string]interface{} { 
	if dto == nil {
		return nil
	}

	fieldMap, err := reflectext.GetStructValidMap(dto.ExtendUserInfo)
	if err != nil {
		logrus.Errorf("reflectext.GetStructValidMap error: %v", err)
		return nil
	}

	return fieldMap
}
