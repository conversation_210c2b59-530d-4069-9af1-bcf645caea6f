package model

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

type AccountPlayer struct {
	PlayerId uint64 `json:"player_id"`
	Password string `json:"password"`
}

func NewAccountPlayer(playerId uint64, password string) *AccountPlayer {
	return &AccountPlayer{
		PlayerId: playerId,
		Password: password,
	}
}

// ToRedisHash 转换为redis hash
func (data AccountPlayer) ToRedisHash() map[string]interface{} {
	hash := make(map[string]any)
	err := transform.Struct2Map(data, hash)
	if err != nil {
		logrus.Warnf("hash convert to redis hash failed:%+v", err)
		return nil
	}
	return hash
}

// NewAccountPlayerFromRdsHash 从redis hash 转换为AccountPlayer
func NewAccountPlayerFromRdsHash(data map[string]string) *AccountPlayer {
	if len(data) == 0 {
		return nil
	}
	obj := &AccountPlayer{}
	transform.Map2Struct(data, obj)
	return obj
}