package model

import (
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

const TableTPlayRealNameAuth = "t_player_real_name_auth"

type TPlayerRealNameAuth struct {
	ProductId  int32     `xorm:"pk not null comment('产品ID') int(10)" json:"product_id"`
	PlayerId   uint64    `xorm:"pk not null comment('玩家ID') BIGINT(20)" json:"player_id"`
	Pi         string    `xorm:"not null default '' comment('实名认证返回pi')" json:"pi"`
	RealName   string    `xorm:"not null default '' comment('真实姓名')" json:"real_name"`
	IdCardNum  string    `xorm:"not null default '' comment('身份证号码')" json:"id_card_num"`
	Year       int32     `xorm:"not null default 0 comment('年')" json:"year"`
	Month      int32     `xorm:"not null default 0 comment('月')" json:"month"`
	Day        int32     `xorm:"not null default 0 comment('日')" json:"day"`
	CreateTime time.Time `xorm:"not null comment('创建时间') DATETIME created" json:"create_time"`
}

func (t *TPlayerRealNameAuth) TableName() string {
	return TableTPlayRealNameAuth
}

func NewRealNameAuthFromProto(proto *commonPB.PlayerRealNameAuth) *TPlayerRealNameAuth {
	return &TPlayerRealNameAuth{
		ProductId: proto.ProductId,
		PlayerId:  proto.PlayerId,
		Pi:        proto.Pi,
		RealName:  proto.RealName,
		IdCardNum: proto.IdCard,
		Year:      proto.Year,
		Month:     proto.Month,
		Day:       proto.Day,
	}
}

func (t *TPlayerRealNameAuth) FromHash(hash map[string]string) error {
	err := transform.Map2Struct(hash, t)
	return err
}

func (t *TPlayerRealNameAuth) ToHash() (map[string]interface{}, error) {
	hash := make(map[string]interface{})
	err := transform.Struct2Map(t, hash)
	if err != nil {
		return nil, err
	}
	return hash, nil
}
