package services

import (
	"context"
	"testing"
	"usersrv/internal/test"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/frameworks/dict"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func init() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     "fancy_player",
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBGame:   conf,
		dict_redis.RDBPlayer: conf,
	})
}

func TestGetPlayerIdByDevice(t *testing.T) {
	cxt := context.Background()
	req := &userRpc.GetPlayerIdByDeviceReq{
		ProductId:  1,
		DeviceCode: "c13bf6c357e9faf6e6445b4622d7afab798aaae0",
	}
	rsp, err := GetPlayerIdByDevice(cxt, req)
	t.Logf("rsp:%v, err:%v", rsp, err)
}

func TestGetPlayerInfoByUid(t *testing.T) {
	test.InitSql()
	test.InitRedis()

	ctx := context.Background()
	req := &userRpc.GetPlayerInfoReq{
		ProductId: 1,
		PlayerId:  94,
	}
	rsp, err := GetPlayerInfoByUid(ctx, req)
	if err != nil {
		t.Errorf("GetPlayerInfoByUid failed, err:%v", err)
		return
	}

	t.Logf("rsp:%v", rsp)
}

func TestGetPlayerDeviceInfoById(t *testing.T) {
	ctx := context.Background()
	req := &userRpc.GetPlayerDeviceInfoReq{
		PlayerId: 179,
	}
	rsp, err := GetPlayerDeviceInfoById(ctx, req)
	if err != nil {
		t.Errorf("GetPlayerDeviceInfoById failed, err:%v", err)
		return
	}
	t.Logf("rsp:%v", rsp)
}


func Test_fetchPlayerInfos_InvalidProductId(t *testing.T) {
	test.InitSql()
	test.InitRedis()

	ctx := context.Background()

	playerIds := []uint64{93, 94, 95}

	_, err := fetchPlayerBriefInfos(ctx, 1, playerIds, logrus.NewEntry(logrus.StandardLogger()))
	if err == nil {
		t.Errorf("Expected error for invalid productId, got nil")
	}
}

func TestQueryPlayerBriefInfo(t *testing.T) {
	test.InitSql()
	test.InitRedis()

	ctx := context.Background()
	req := &userRpc.QueryPlayerBriefInfoReq{
		ProductId: 1,
		PlayerId:  94,
	}

	rsp, err := QueryPlayerBriefInfo(ctx, req)
	if err != nil {
		t.Errorf("QueryPlayerBriefInfo failed, err:%v", err)
		return
	}

	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		t.Errorf("QueryPlayerBriefInfo failed, code:%v", rsp.Ret.Code)
		return
	}

	if rsp.PlayerInfo == nil {
		t.Error("QueryPlayerBriefInfo failed, playerInfo is nil")
		return
	}

	t.Logf("rsp:%+v", rsp)
}