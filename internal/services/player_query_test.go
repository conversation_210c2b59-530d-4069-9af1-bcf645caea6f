package services

import (
	"context"
	"testing"
	"time"
	"usersrv/internal/test"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/frameworks/dict"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func init() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     "fancy_player",
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBGame:   conf,
		dict_redis.RDBPlayer: conf,
	})
}

func TestGetPlayerIdByDevice(t *testing.T) {
	cxt := context.Background()
	req := &userRpc.GetPlayerIdByDeviceReq{
		ProductId:  1,
		DeviceCode: "c13bf6c357e9faf6e6445b4622d7afab798aaae0",
	}
	rsp, err := GetPlayerIdByDevice(cxt, req)
	t.Logf("rsp:%v, err:%v", rsp, err)
}

func TestGetPlayerInfoByUid(t *testing.T) {
	ctx := context.Background()
	req := &userRpc.GetPlayerInfoReq{
		ProductId: 1,
		PlayerId:  224,
	}
	rsp, err := GetPlayerInfoByUid(ctx, req)
	if err != nil {
		t.Errorf("GetPlayerInfoByUid failed, err:%v", err)
		return
	}

	t.Logf("rsp:%v", rsp)
}

func TestGetPlayerDeviceInfoById(t *testing.T) {
	ctx := context.Background()
	req := &userRpc.GetPlayerDeviceInfoReq{
		PlayerId: 179,
	}
	rsp, err := GetPlayerDeviceInfoById(ctx, req)
	if err != nil {
		t.Errorf("GetPlayerDeviceInfoById failed, err:%v", err)
		return
	}
	t.Logf("rsp:%v", rsp)
}

func Test_fetchPlayerInfos(t *testing.T) {
	// 初始化测试环境
	test.InitSql()
	test.InitRedis()

	ctx := context.Background()
	productId := int32(1)
	entry := logrus.NewEntry(logrus.StandardLogger())

	// 性能测试
	t.Run("Performance test", func(t *testing.T) {
		largePlayerIds := make([]uint64, 1000)
		for i := range largePlayerIds {
			largePlayerIds[i] = uint64(i + 1)
		}

		start := time.Now()
		rsp, err := fetchPlayerInfos(ctx, productId, largePlayerIds, entry)
		duration := time.Since(start)

		if err != nil {
			t.Errorf("Performance test failed: %v", err)
			return
		}

		t.Logf("Fetched %d players in %v", len(rsp), duration)
		if duration > 5*time.Second {
			t.Errorf("Performance test took too long: %v", duration)
		}
	})
}

func Test_fetchPlayerInfos1(t *testing.T) {
	test.InitSql()
	test.InitRedis()

	ctx := context.Background()

	playerIds := make([]uint64, 0, 1000)
	for i := 1; i < 1000; i++ {
		playerIds = append(playerIds, uint64(i))
	}

	rsp, err := fetchPlayerInfos(ctx, 1, playerIds, logrus.NewEntry(logrus.StandardLogger()))
	if err != nil {
		t.Errorf("fetchPlayerInfos failed, err:%v", err)
		return
	}

	t.Logf("rsp:%v", rsp)
}

func Test_fetchPlayerInfos_EmptyPlayerIds(t *testing.T) {
	test.InitSql()
	test.InitRedis()

	ctx := context.Background()

	playerIds := []uint64{}

	rsp, err := fetchPlayerInfos(ctx, 1, playerIds, logrus.NewEntry(logrus.StandardLogger()))
	if err != nil {
		t.Errorf("fetchPlayerInfos failed, err:%v", err)
		return
	}

	if len(rsp) != 0 {
		t.Errorf("Expected empty response, got: %v", rsp)
	}
}

func Test_fetchPlayerInfos_InvalidProductId(t *testing.T) {
	test.InitSql()
	test.InitRedis()

	ctx := context.Background()

	playerIds := []uint64{1, 2, 3}

	_, err := fetchPlayerInfos(ctx, -1, playerIds, logrus.NewEntry(logrus.StandardLogger()))
	if err == nil {
		t.Errorf("Expected error for invalid productId, got nil")
	}
}
