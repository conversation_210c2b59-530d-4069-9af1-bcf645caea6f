package services

import (
	"context"
	"errors"
	logicDelete "usersrv/internal/logic/logic_delete"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

func DeleteAccount(ctx context.Context, req *userRpc.DeleteAccountReq) (*userRpc.DeleteAccountRsp, error) {
	rsp := &userRpc.DeleteAccountRsp{
		Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL),
	}
	entry := logx.NewLogEntry(ctx)

	playerId := req.GetPlayerId()
	productId := req.GetProductId()
	if productId <= 0 || playerId <= 0 {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		entry.Errorf("delete productId:%d player:%d, param error", productId, playerId)
		return rsp, errors.New("param error")
	}

	err := logicDelete.DeleteAccount(ctx, productId, playerId)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL, err.Error())
		entry.Errorf("delete player:%d, err:%v", playerId, err)
		return rsp, err
	}

	entry.Infof("delete player:%d success", playerId)

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}
