package services

import (
	"context"
	"errors"
	"usersrv/internal/dao/dao_master"
	logicQuery "usersrv/internal/logic/logic_query"
	"usersrv/internal/model"

	"github.com/sirupsen/logrus"

	logicCommon "usersrv/internal/logic/logic_common"

	logicCreate "usersrv/internal/logic/logic_create"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// GetPlayerIdByDevice 根据平台和设备获取玩家ID
func GetPlayerIdByDevice(ctx context.Context, req *userRpc.GetPlayerIdByDeviceReq) (*userRpc.GetPlayerIdByDeviceRsp, error) {
	productId := req.GetProductId()
	deviceCode := req.GetDeviceCode()
	entry := logx.NewLogEntry(ctx)

	if productId <= 0 || len(deviceCode) == 0 {
		entry.Errorf("GetPlayerIdByProductDevice, productId:%d, deviceCode:%s", productId, deviceCode)
		return nil, errors.New("productId or deviceCode is empty")
	}

	playerId, err := logicCreate.QueryPlayerIdByDeviceCode(ctx, productId, deviceCode, req.GetLoginType())
	if err != nil {
		entry.Errorf("get player id error, productId:%d, deviceCode:%s, err:%v", productId, deviceCode, err)
		return nil, errors.New("get playerId error " + err.Error())
	}

	rsp := &userRpc.GetPlayerIdByDeviceRsp{
		PlayerId: playerId,
	}

	entry.Debugf("get playerId:%d, productId:%d, deviceCode:%s", playerId, productId, deviceCode)

	return rsp, nil
}

// GetPlayerInfoByUid 根据uid查询玩家信息
func GetPlayerInfoByUid(ctx context.Context, req *userRpc.GetPlayerInfoReq) (*userRpc.GetPlayerInfoRsp, error) {
	productId := req.GetProductId()
	playerId := req.GetPlayerId()
	entry := logx.NewLogEntry(ctx)

	if playerId <= 0 {
		entry.Errorf("query playerId:%d info param error", playerId)
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	// 获取玩家信息
	pMasterInfo, playerInfo, err := logicQuery.QueryPlayerInfoById(ctx, productId, playerId)
	if err != nil {
		entry.Errorf("query playerId:%d, base info err:%v", playerId, err)
		return nil, err
	}

	// 查询玩家拓展信息
	extendInfo, err := logicQuery.QueryPlayerExtend(ctx, productId, playerId)
	if err != nil {
		entry.Warnf("query playerId:%d, extend info ret:%v", playerId, err)
	}

	pUserInfo, err := model.MakeUpRichUserInfo(pMasterInfo, playerInfo, extendInfo)
	if err != nil {
		entry.Errorf("makeup playerId:%d, info err:%v", playerId, err)
		return nil, errors.New("MakeUpRichUserInfo error")
	}

	rsp := &userRpc.GetPlayerInfoRsp{
		PlayerId:     pMasterInfo.PlayerID,
		RichUserInfo: pUserInfo,
	}

	entry.Infof("GetPlayerInfoByUid, playerId:%d, rsp:%v", playerId, rsp)

	return rsp, nil
}

// GetPlayerDeviceInfoById 查询玩家设备信息
func GetPlayerDeviceInfoById(ctx context.Context, req *userRpc.GetPlayerDeviceInfoReq) (*userRpc.GetPlayerDeviceInfoRsp, error) {
	productId := req.GetProductId()
	playerId := req.GetPlayerId()
	entry := logx.NewLogEntry(ctx)

	rsp := &userRpc.GetPlayerDeviceInfoRsp{}

	if playerId <= 0 {
		entry.Errorf("get player:%d info param error", playerId)
		return rsp, errors.New("param error")
	}

	// 获取玩家信息
	pDeviceInfo, err := logicQuery.QueryPlayerDeviceInfo(ctx, productId, playerId)
	if err != nil || pDeviceInfo == nil {
		entry.Errorf("GetPlayerInfoByUid, playerId:%d, err:%v", playerId, err)
		return rsp, err
	}
	rsp.DeviceInfo = pDeviceInfo.ToProto()

	entry.Debugf("GetPlayerDeviceInfoById, playerId:%d, rsp:%v", playerId, rsp)

	return rsp, nil
}

// GetPlayerIdByAccount 根据账号查询玩家信息
func GetPlayerIdByAccount(ctx context.Context, req *userRpc.GetPlayerIdByAccountReq) (*userRpc.GetPlayerIdByAccountRsp, error) {
	productId := req.GetProductId()
	accountInfo := req.GetAccountInfo()
	entry := logx.NewLogEntry(ctx)

	if productId <= 0 || accountInfo == nil {
		entry.Errorf("GetPlayerIdByProductDevice, productId:%d, account info is nil", productId)
		return &userRpc.GetPlayerIdByAccountRsp{}, errors.New("productId or account is empty")
	}

	if accountInfo.GetAccount() == "" {
		entry.Errorf("GetPlayerIdByProductDevice, productId:%d, account info:%+v error", productId, accountInfo)
		return &userRpc.GetPlayerIdByAccountRsp{}, errors.New("account info error")
	}

	playerId, err := logicCreate.QueryPlayerIdByAccount(ctx, productId, accountInfo.GetAccount(), accountInfo.GetPassword())
	if err != nil {
		entry.Errorf("get player id error, productId:%d, account:%+v, err:%v", productId, accountInfo, err)
		return &userRpc.GetPlayerIdByAccountRsp{}, errors.New("get playerId error")
	}

	rsp := &userRpc.GetPlayerIdByAccountRsp{
		PlayerId: playerId,
	}

	entry.Debugf("get playerId:%d, productId:%d, account:%+v", playerId, productId, accountInfo)

	return rsp, nil
}

// PlayerMultiQuery 批量获取玩家信息
func PlayerMultiQuery(ctx context.Context, req *userRpc.PlayerMultiQueryReq) (*userRpc.PlayerMultiQueryRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("PlayerMultiQuery req:%+v", req)

	productId := req.GetProductId()
	rsp := &userRpc.PlayerMultiQueryRsp{
		Ret: protox.DefaultResult(),
	}
	rsp.PlayerInfo = make(map[uint64]*commonPB.RichUserInfo, 0)

	// 批量查询玩家信息
	playerInfo, err := fetchPlayerRichInfos(ctx, productId, req.GetPlayerId(), entry)
	if err != nil {
		entry.Errorf("BatchPlayerInfo, req:%+v, err:%v", req, err)
		return rsp, err
	}

	for _, pInfo := range playerInfo {
		rsp.PlayerInfo[pInfo.BriefUserInfo.PlayerId] = pInfo
	}

	return rsp, nil
}

// BatchPlayerInfo 后台获取用户信息
func BatchPlayerInfo(ctx context.Context, req *userRpc.BatchPlayerInfoReq) (*userRpc.BatchPlayerInfoRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("BatchPlayerInfo req:%+v", req)

	// 获取产品ID并检查其有效性
	productId := req.GetProductId()
	if productId == 0 {
		return &userRpc.BatchPlayerInfoRsp{
			Ret: protox.DefaultResult(),
		}, errors.New("product ID is invalid")
	}

	// 初始化响应结构体
	rsp := &userRpc.BatchPlayerInfoRsp{
		Ret: protox.DefaultResult(),
	}

	// 检查分页信息是否存在
	if req.Pagination == nil {
		return rsp, errors.New("pagination is nil")
	}

	// 初始化玩家信息切片
	rsp.PlayerInfo = make([]*commonPB.RichUserInfo, 0, req.Pagination.PageSize)

	// 获取玩家ID列表和总数量
	playerIds, total, err := getPlayerIds(req, productId)
	if err != nil {
		entry.Errorf("BatchPlayerInfo, req:%+v, err:%v", req, err)
		return rsp, err
	}

	rsp.Count = int32(total)

	// 批量查询玩家信息
	rsp.PlayerInfo, err = fetchPlayerRichInfos(ctx, productId, playerIds, entry)
	if err != nil {
		entry.Errorf("BatchPlayerInfo, req:%+v, err:%v", req, err)
		return rsp, err
	}

	// 如果总数量为0，则设置为实际返回的玩家信息数量
	if rsp.Count == 0 {
		rsp.Count = int32(len(rsp.PlayerInfo))
	}

	// 设置成功响应码
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Infof("BatchPlayerInfo req:%+v login success ", req)

	return rsp, nil
}

// getPlayerIds 根据请求获取玩家ID列表和总数量
func getPlayerIds(req *userRpc.BatchPlayerInfoReq, productId int32) ([]uint64, int64, error) {
	if req.PlayerId != 0 {
		return []uint64{req.PlayerId}, 1, nil
	}
	return dao_master.Batch(productId, req.Pagination)
}

// fetchPlayerRichInfos 批量查询玩家全量信息 已处理:20250317 clh
func fetchPlayerRichInfos(ctx context.Context, productId int32, playerIds []uint64, entry *logrus.Entry) ([]*commonPB.RichUserInfo, error) {
	playerInfos := make([]*commonPB.RichUserInfo, 0)
	mapMasterInfo, mapPlayerInfo, err := logicQuery.BatchQueryPlayerRichInfo(ctx, productId, playerIds)
	if err != nil || len(mapMasterInfo) == 0 || len(mapPlayerInfo) == 0 {
		entry.Errorf("BatchPlayerInfo failed, productId:%d, playerIds:%+v, err:%v", productId, playerIds, err)
		return nil, err
	}
	for _, playerId := range playerIds {
		pUserInfo, err := model.MakeUpRichUserInfo(mapMasterInfo[playerId], mapPlayerInfo[playerId], nil)
		if err != nil {
			entry.Warnf("BatchPlayerInfo failed, playerId:%d not found warn:%+v", playerId, err)
			continue
		}
		playerInfos = append(playerInfos, pUserInfo)
	}

	return playerInfos, nil
}

// fetchPlayerBriefInfos 批量查询玩家简要信息
func fetchPlayerBriefInfos(ctx context.Context, productId int32, playerIds []uint64, entry *logrus.Entry) ([]*commonPB.BriefUserInfo, error) {
	playerInfos := make([]*commonPB.BriefUserInfo, 0)

	mapPlayerInfo, err := logicQuery.BatchQueryPlayerBriefInfo(ctx, productId, playerIds)
	if err != nil || len(mapPlayerInfo) == 0 {
		entry.Errorf("BatchPlayerBriefInfo failed, productId:%d, playerIds:%+v, err:%v", productId, playerIds, err)
		return nil, err
	}

	for _, playerId := range playerIds {
		pUserInfo, err := model.MakeUpBriefUserInfo(mapPlayerInfo[playerId])
		if err != nil {
			entry.Errorf("BatchPlayerBriefInfo failed, playerId:%d, err:%v", playerId, err)
			continue
		}
		playerInfos = append(playerInfos, pUserInfo)
	}

	return playerInfos, nil
}

// GetPlayerIdByOpenId 根据openId和登录方式查询玩家id
func GetPlayerIdByOpenId(ctx context.Context, req *userRpc.GetPlayerIdByOpenIdReq) (*userRpc.GetPlayerIdByOpenIdRsp, error) {
	entry := logx.NewLogEntry(ctx)

	playerId, err := logicCreate.QueryPlayerIdByOpenId(ctx, req.GetProductId(), int32(logicCommon.ConvertLoginTypeToAccType(req.GetLoginType())), req.GetOpenId())
	if err != nil {
		entry.Errorf("GetPlayerIdByOpenId, req:%+v, err:%v", req, err)
		return nil, err
	}

	rsp := &userRpc.GetPlayerIdByOpenIdRsp{
		PlayerId: playerId,
	}

	entry.Debugf("GetPlayerIdByOpenId, req:%+v, rsp:%+v", req, rsp)

	return rsp, nil
}

// QueryPlayerExtendInfo 查询玩家扩展信息
func QueryPlayerExtendInfo(ctx context.Context, req *userRpc.QueryPlayerExtendInfoReq) (*userRpc.QueryPlayerExtendInfoRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("GetPlayerExtendInfo req:%+v", req)

	playerExtend, err := logicQuery.QueryPlayerExtend(ctx, req.GetProductId(), req.GetPlayerId())
	if err != nil && !errors.Is(err, model.PlayerNil) {
		entry.Errorf("GetPlayerExtendInfo, req:%+v, err:%v", req, err)
		return nil, err
	}

	// 如果没有查询到玩家扩展信息，则创建一个新的PlayerExtend对象并返回默认值
	if playerExtend == nil {
		playerExtend = &model.PlayerExtend{PlayerID: req.GetPlayerId()}
	}

	rsp := &userRpc.QueryPlayerExtendInfoRsp{
		ExtendInfo: playerExtend.ToProto(),
		Ret:        protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS),
	}

	entry.Debugf("GetPlayerExtendInfo, req:%+v, rsp:%+v", req, rsp)

	return rsp, nil
}

// BatchPlayerBriefInfo 批量查询玩家简要信息
func BatchPlayerBriefInfo(ctx context.Context, req *userRpc.BatchPlayerBriefInfoReq) (*userRpc.BatchPlayerBriefInfoRsp, error) {
	entry := logx.NewLogEntry(ctx)

	entry.Debugf("BatchPlayerBriefInfo req:%+v", req)

	rsp := &userRpc.BatchPlayerBriefInfoRsp{}

	playerInfos, err := fetchPlayerBriefInfos(ctx, req.GetProductId(), req.GetPlayerList(), entry)
	if err != nil {
		entry.Errorf("BatchPlayerBriefInfo, req:%+v, err:%v", req, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, err
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.PlayerInfo = playerInfos

	entry.Debugf("BatchPlayerBriefInfo, req:%+v, rsp:%+v", req, rsp)

	return rsp, nil
}
