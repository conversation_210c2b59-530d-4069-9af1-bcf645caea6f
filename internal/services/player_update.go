package services

import (
	"context"
	"errors"
	"usersrv/internal/config"
	logicUpdate "usersrv/internal/logic/logic_update"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// UpdatePlayerInfoById 根据玩家id 动态更新玩家信息
func UpdatePlayerInfoById(ctx context.Context, req *userRpc.UpdatePlayerInfoReq) (*userRpc.UpdatePlayerInfoRsp, error) {
	rsp := &userRpc.UpdatePlayerInfoRsp{
		Ret: protox.DefaultResult(),
	}

	entry := logx.NewLogEntry(ctx)
	productId := req.GetProductId()
	playerId := req.GetPlayerId()
	if playerId <= 0 || len(req.GetUpdateMap()) == 0 {
		entry.Errorf("update player:%d info param:%s error", playerId, req.GetUpdateMap())
		return rsp, errors.New("param error")
	}

	updateParams := make(map[string]interface{})
	// TODO: 检查sql注入
	// TODO: 针对性检查
	for key, val := range req.UpdateMap {
		updateParams[key] = val
	}

	err := logicUpdate.UpdatePlayerInfoDynamic(ctx, productId, playerId, config.PlayerInfoKey(productId, playerId), model.TablePlayerInfo, updateParams)
	if err != nil {
		entry.Errorf("update player:%d info:%v logic err:%v", playerId, updateParams, err)
		return rsp, err
	}

	rsp = &userRpc.UpdatePlayerInfoRsp{
		PlayerId:  playerId,
		UpdateMap: req.UpdateMap,
	}

	entry.Infof("update player:%d req:%s rsp:%s success", playerId, req.String(), rsp.String())

	return rsp, nil
}

// PlayerLoginUpdate  用户登录后更新信息
func PlayerLoginUpdate(ctx context.Context, req *userRpc.UpdateLoginReq) (*userRpc.UpdateLoginRsp, error) {
	rsp := &userRpc.UpdateLoginRsp{
		Ret: protox.DefaultResult(),
	}

	entry := logx.NewLogEntry(ctx)
	entry.Infof("PlayerLoginUpdate, req:%+v", req)

	productId := req.GetProductId()
	playerId := req.GetPlayerId()
	if playerId <= 0 || productId == 0 {
		return rsp, errors.New("param error")
	}
	dto := &model.UpdateLoginDto{DeviceInfo: req.GetDeviceInfo()}
	err := logicUpdate.UpdatePlayerLogin(ctx, productId, playerId, dto)
	if err != nil {
		entry.Errorf("update player:%d login error:%v", playerId, err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
		return rsp, err
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Infof("update player:%d login success ", playerId)
	
	return rsp, nil
}

// UpdatePlayerExtendInfo 更新玩家扩展信息
func UpdatePlayerExtendInfo(ctx context.Context, req *userRpc.UpdatePlayerExtendInfoReq) (*userRpc.UpdatePlayerExtendInfoRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Debugf("UpdatePlayerExtendInfo req:%+v", req)

	rsp := &userRpc.UpdatePlayerExtendInfoRsp{
		Ret: protox.DefaultResult(),
	}

	dto := &model.UpdateExtendDto{ExtendUserInfo: req.GetExtendInfo()}
	err := logicUpdate.UpdatePlayerExtendInfo(ctx, req.GetProductId(), req.GetPlayerId(), dto)

	if err != nil {
		entry.Errorf("UpdatePlayerExtendInfo, req:%+v, err:%v", req, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp, err
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	return rsp, nil
}