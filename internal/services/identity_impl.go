package services

import (
	"context"
	"usersrv/internal/logic/logic_identity"
	"usersrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// RealNameAuthQuery 查询实名认证
func RealNameAuthQuery(ctx context.Context, req *userRpc.RealNameAuthQueryReq) (*userRpc.RealNameAuthQueryRsp, error) {
	playerId := req.GetPlayerId()
	productId := req.GetProductId()
	rsp := &userRpc.RealNameAuthQueryRsp{
		Ret:        protox.DefaultResult(),
		IsRealName: false,
	}

	if productId == 0 || playerId == 0 {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		return rsp, nil
	}
	flag, err := logic_identity.RealNameStatus(ctx, productId, playerId)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
		return rsp, nil
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.IsRealName = flag

	return rsp, nil
}

// RealNameAuthUpdate 更新实名认证
func RealNameAuthUpdate(ctx context.Context, req *userRpc.RealNameAuthReq) (*userRpc.RealNameAuthRsp, error) {
	rsp := &userRpc.RealNameAuthRsp{
		Ret: protox.DefaultResult(),
	}

	info := req.GetAuthInfo()
	if info == nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, "authInfo is nil")
		return rsp, nil
	}

	// 加上事务处理
	err := logic_identity.UpdateRealName(ctx, model.NewRealNameAuthFromProto(info))
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
		return rsp, nil
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}

// PlayerAgeQuery 用户年龄查询
func PlayerAgeQuery(ctx context.Context, req *userRpc.PlayerAgeQueryReq) (*userRpc.PlayerAgeQueryRsp, error) {
	rsp := &userRpc.PlayerAgeQueryRsp{
		Ret: protox.DefaultResult(),
	}

	// 加上事务处理
	data, err := logic_identity.PlayerAgeQuery(ctx, req.GetProductId(), req.GetPlayerId())
	if err != nil {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
		return rsp, nil
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.Age = data

	return rsp, nil
}
