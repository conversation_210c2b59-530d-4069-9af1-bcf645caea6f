package services

import (
	"context"
	"errors"
	logicCreate "usersrv/internal/logic/logic_create"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
)

// CreatePlayer 创建用户逻辑，在这里调用dao的 create user函数
func CreatePlayer(ctx context.Context, req *userRpc.CreatePlayerReq) (*userRpc.CreatePlayerRsp, error) {
	rsp := &userRpc.CreatePlayerRsp{}
	createType := req.GetCreateType()
	var ICreate logicCreate.ICreate

	switch createType {
	case commonPB.LOGIN_TYPE_LT_VISITOR:
		ICreate = new(logicCreate.CreateVisitor)
	case commonPB.LOGIN_TYPE_LT_PASSWORD:
		ICreate = new(logicCreate.CreateAccount)
	// 第三方登录都走这里(google, apple, facebook, etc.)
	case commonPB.LOGIN_TYPE_LT_GOOGLE, commonPB.LOGIN_TYPE_LT_FACEBOOK, commonPB.LOGIN_TYPE_LT_APPLE:
		ICreate = new(logicCreate.CreateThird)
	default:
		return rsp, errors.New("create type is not valid")
	}

	return ICreate.CreatePlayer(ctx, req)
}
