package services

import (
	"context"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func init() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     "fancy_player",
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBPlayer: conf,
		dict_redis.RDBLock:   conf,
	})
}

func TestUpdatePlayerInfoById(t *testing.T) {
	// param := model.UpdatePlayerParam{
	// 	NickName: "choose2",
	// }
	// paramByte, _ := json.Marshal(param)
	req := &userRpc.UpdatePlayerInfoReq{
		PlayerId: 179,
		// UpdateJson: string(paramByte),
	}
	rsp, err := UpdatePlayerInfoById(context.Background(), req)
	t.Logf("rsp: %v, err:%v", rsp, err)
}
