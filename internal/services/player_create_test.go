package services

import (
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func init() {
	confUser := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     "fancy_player",
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBPlayer: confUser,
	})

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBPlayer: conf,
		dict_redis.RDBLock:   conf,
	})
}

func TestCreatePlayerByDeviceCode(t *testing.T) {
	// req := &userRpc.CreatePlayerReq{
	// 	ProductId: 2,
	// 	ClientVersion:"test_client_version",
	// 	DeviceInfo: &commonPB.DeviceInfo{
	// 		DeviceCode: "test_device_code4",
	// 		DeviceName: "test_device_name",
	// 	},
	// 	BundleName:"test_bundle_name",
	// 	ChannelId: 1,
	// }

	// expireTime := config.PLAYER_EXPIRE
	// t.Logf("expireTime:%v", expireTime)
	// rsp, err := CreatePlayerByDeviceCode(context.Background(), req)
	// t.Logf("CreatePlayerByDeviceCode success:%v, err:%v", rsp, err)
}
