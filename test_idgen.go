package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
	logicCommon "usersrv/internal/logic/logic_common"
	"usersrv/internal/test"
)

func main() {
	fmt.Println("开始测试 GeneratePlayerID 函数...")
	
	// 初始化测试环境
	test.InitSql()
	test.InitRedis()
	
	// 运行各种测试
	testBasicGeneration()
	testConcurrentGeneration()
	testSequentialGeneration()
	testDifferentProducts()
	testContextCancellation()
	testPerformance()
	
	fmt.Println("所有测试完成!")
}

// 基本生成测试
func testBasicGeneration() {
	fmt.Println("\n=== 基本生成测试 ===")
	
	ctx := context.Background()
	productId := int32(1)
	
	for i := 0; i < 5; i++ {
		id, err := logicCommon.GeneratePlayerID(ctx, productId)
		if err != nil {
			log.Printf("生成ID失败: %v", err)
			continue
		}
		
		if id == 0 {
			log.Printf("生成的ID为0，这是不正确的")
			continue
		}
		
		fmt.Printf("第 %d 次生成的ID: %d\n", i+1, id)
	}
}

// 并发生成测试
func testConcurrentGeneration() {
	fmt.Println("\n=== 并发生成测试 ===")
	
	ctx := context.Background()
	productId := int32(1)
	
	const goroutineCount = 10
	const idsPerGoroutine = 5
	
	var wg sync.WaitGroup
	var mu sync.Mutex
	var allIds []uint64
	var errors []error
	
	for i := 0; i < goroutineCount; i++ {
		wg.Add(1)
		go func(routineId int) {
			defer wg.Done()
			
			var localIds []uint64
			for j := 0; j < idsPerGoroutine; j++ {
				id, err := logicCommon.GeneratePlayerID(ctx, productId)
				if err != nil {
					mu.Lock()
					errors = append(errors, err)
					mu.Unlock()
					return
				}
				localIds = append(localIds, id)
			}
			
			mu.Lock()
			allIds = append(allIds, localIds...)
			mu.Unlock()
			
			fmt.Printf("Goroutine %d 生成了 %d 个ID\n", routineId, len(localIds))
		}(i)
	}
	
	wg.Wait()
	
	if len(errors) > 0 {
		fmt.Printf("并发测试中出现 %d 个错误:\n", len(errors))
		for _, err := range errors {
			fmt.Printf("  - %v\n", err)
		}
		return
	}
	
	// 检查唯一性
	idSet := make(map[uint64]bool)
	duplicates := 0
	for _, id := range allIds {
		if idSet[id] {
			duplicates++
		}
		idSet[id] = true
	}
	
	fmt.Printf("并发生成了 %d 个ID，其中 %d 个重复\n", len(allIds), duplicates)
	if duplicates == 0 {
		fmt.Println("✓ 所有ID都是唯一的")
	} else {
		fmt.Printf("✗ 发现 %d 个重复ID\n", duplicates)
	}
}

// 连续生成测试
func testSequentialGeneration() {
	fmt.Println("\n=== 连续生成测试 ===")
	
	ctx := context.Background()
	productId := int32(1)
	
	var ids []uint64
	for i := 0; i < 10; i++ {
		id, err := logicCommon.GeneratePlayerID(ctx, productId)
		if err != nil {
			log.Printf("生成ID失败: %v", err)
			continue
		}
		ids = append(ids, id)
		fmt.Printf("ID %d: %d\n", i+1, id)
	}
	
	// 检查是否有重复
	idSet := make(map[uint64]bool)
	duplicates := 0
	for _, id := range ids {
		if idSet[id] {
			duplicates++
		}
		idSet[id] = true
	}
	
	if duplicates == 0 {
		fmt.Println("✓ 连续生成的ID都是唯一的")
	} else {
		fmt.Printf("✗ 连续生成中发现 %d 个重复ID\n", duplicates)
	}
}

// 不同产品ID测试
func testDifferentProducts() {
	fmt.Println("\n=== 不同产品ID测试 ===")
	
	ctx := context.Background()
	productIds := []int32{1, 2, 100, 999}
	
	for _, productId := range productIds {
		id, err := logicCommon.GeneratePlayerID(ctx, productId)
		if err != nil {
			fmt.Printf("产品ID %d 生成失败: %v\n", productId, err)
			continue
		}
		fmt.Printf("产品ID %d 生成的玩家ID: %d\n", productId, id)
	}
}

// 上下文取消测试
func testContextCancellation() {
	fmt.Println("\n=== 上下文取消测试 ===")
	
	productId := int32(1)
	
	// 创建一个会被取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消
	
	id, err := logicCommon.GeneratePlayerID(ctx, productId)
	fmt.Printf("使用已取消的上下文: ID=%d, err=%v\n", id, err)
	
	// 创建一个带超时的上下文
	ctx2, cancel2 := context.WithTimeout(context.Background(), 1*time.Millisecond)
	defer cancel2()
	
	// 等待超时
	time.Sleep(2 * time.Millisecond)
	
	id2, err2 := logicCommon.GeneratePlayerID(ctx2, productId)
	fmt.Printf("使用超时上下文: ID=%d, err=%v\n", id2, err2)
}

// 性能测试
func testPerformance() {
	fmt.Println("\n=== 性能测试 ===")
	
	ctx := context.Background()
	productId := int32(1)
	
	// 单线程性能测试
	count := 1000
	start := time.Now()
	
	successCount := 0
	for i := 0; i < count; i++ {
		_, err := logicCommon.GeneratePlayerID(ctx, productId)
		if err == nil {
			successCount++
		}
	}
	
	duration := time.Since(start)
	fmt.Printf("单线程生成 %d 个ID，成功 %d 个，耗时: %v\n", count, successCount, duration)
	fmt.Printf("平均每个ID耗时: %v\n", duration/time.Duration(successCount))
	
	if successCount > 0 {
		fmt.Printf("QPS: %.2f\n", float64(successCount)/duration.Seconds())
	}
}
